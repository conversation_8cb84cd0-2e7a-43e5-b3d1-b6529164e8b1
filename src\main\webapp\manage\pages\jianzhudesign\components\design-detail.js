// 建筑设计详情组件
Vue.component('design-detail', {
    props: {
        designData: {
            type: Object,
            default: () => ({})
        }
    },
    
    data() {
        return {
            activeTab: 'overview',
            loading: false
        }
    },
    
    computed: {
        statusType() {
            switch (this.designData.auditstatus) {
                case '已通过':
                    return 'success';
                case '待审核':
                    return 'warning';
                case '已拒绝':
                    return 'danger';
                default:
                    return 'info';
            }
        }
    },
    
    methods: {
        // 编辑设计
        editDesign() {
            this.$emit('edit', this.designData);
        },
        
        // 审核设计
        auditDesign() {
            this.$emit('audit', this.designData);
        },
        
        // 预览文件
        previewFile(url, type) {
            if (!url) {
                this.$message.warning('没有文件可预览');
                return;
            }
            
            if (type === 'image') {
                // 图片预览
                this.$alert(`<img src="${url}" style="max-width: 100%; max-height: 500px;">`, '图片预览', {
                    dangerouslyUseHTMLString: true
                });
            } else {
                // 其他文件类型打开新窗口
                window.open(url, '_blank');
            }
        },
        
        // 下载文件
        downloadFile(url, filename) {
            if (!url) {
                this.$message.warning('没有文件可下载');
                return;
            }
            
            const link = document.createElement('a');
            link.href = url;
            link.download = filename || '文件';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        
        // 格式化金额
        formatMoney(amount) {
            if (!amount) return '0';
            return Number(amount).toLocaleString();
        }
    },
    
    template: `
        <div class="design-detail">
            <!-- 头部信息 -->
            <div class="detail-header">
                <div class="header-left">
                    <h2>{{designData.designname}}</h2>
                    <p class="design-code">设计编号：{{designData.designcode}}</p>
                </div>
                <div class="header-right">
                    <el-tag :type="statusType" size="large">{{designData.auditstatus}}</el-tag>
                    <div class="action-buttons">
                        <el-button type="primary" icon="el-icon-edit" @click="editDesign">编辑</el-button>
                        <el-button type="success" icon="el-icon-check" @click="auditDesign" v-if="designData.auditstatus === '待审核'">审核</el-button>
                    </div>
                </div>
            </div>
            
            <!-- 主要信息卡片 -->
            <div class="info-cards">
                <div class="info-card">
                    <div class="card-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="card-content">
                        <h4>{{designData.buildingtype}}</h4>
                        <p>建筑类型</p>
                    </div>
                </div>
                <div class="info-card">
                    <div class="card-icon">
                        <i class="fas fa-ruler-combined"></i>
                    </div>
                    <div class="card-content">
                        <h4>{{designData.buildingarea}}㎡</h4>
                        <p>建筑面积</p>
                    </div>
                </div>
                <div class="info-card">
                    <div class="card-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="card-content">
                        <h4>{{designData.floors}}层</h4>
                        <p>建筑层数</p>
                    </div>
                </div>
                <div class="info-card">
                    <div class="card-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="card-content">
                        <h4>¥{{formatMoney(designData.budgetamount)}}</h4>
                        <p>预算金额</p>
                    </div>
                </div>
            </div>
            
            <!-- 详细信息标签页 -->
            <el-tabs v-model="activeTab" type="border-card">
                <!-- 概览 -->
                <el-tab-pane label="设计概览" name="overview">
                    <div class="overview-content">
                        <el-row :gutter="30">
                            <el-col :span="16">
                                <div class="basic-info">
                                    <h3>基本信息</h3>
                                    <el-descriptions :column="2" border>
                                        <el-descriptions-item label="设计编号">{{designData.designcode}}</el-descriptions-item>
                                        <el-descriptions-item label="设计名称">{{designData.designname}}</el-descriptions-item>
                                        <el-descriptions-item label="项目编号">{{designData.xiangmubianhao}}</el-descriptions-item>
                                        <el-descriptions-item label="建筑类型">{{designData.buildingtype}}</el-descriptions-item>
                                        <el-descriptions-item label="建筑风格">{{designData.architecturalstyle}}</el-descriptions-item>
                                        <el-descriptions-item label="结构类型">{{designData.structuretype}}</el-descriptions-item>
                                        <el-descriptions-item label="设计师">{{designData.designer}}</el-descriptions-item>
                                        <el-descriptions-item label="设计日期">{{designData.designdate}}</el-descriptions-item>
                                        <el-descriptions-item label="设计版本">{{designData.designversion}}</el-descriptions-item>
                                        <el-descriptions-item label="审核状态">
                                            <el-tag :type="statusType">{{designData.auditstatus}}</el-tag>
                                        </el-descriptions-item>
                                    </el-descriptions>
                                </div>
                                
                                <div class="parameters-info">
                                    <h3>建筑参数</h3>
                                    <el-descriptions :column="3" border>
                                        <el-descriptions-item label="建筑面积">{{designData.buildingarea}}㎡</el-descriptions-item>
                                        <el-descriptions-item label="建筑高度">{{designData.buildingheight}}m</el-descriptions-item>
                                        <el-descriptions-item label="建筑层数">{{designData.floors}}层</el-descriptions-item>
                                        <el-descriptions-item label="地下层数">{{designData.basementfloors}}层</el-descriptions-item>
                                        <el-descriptions-item label="预算金额">¥{{formatMoney(designData.budgetamount)}}</el-descriptions-item>
                                        <el-descriptions-item label="是否启用">{{designData.isenabled}}</el-descriptions-item>
                                    </el-descriptions>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="rendering-preview">
                                    <h3>效果图预览</h3>
                                    <div class="image-container">
                                        <img :src="designData.renderingimages || '/images/default-building.jpg'" alt="效果图" @click="previewFile(designData.renderingimages, 'image')">
                                        <div class="image-actions">
                                            <el-button size="small" type="primary" @click="previewFile(designData.renderingimages, 'image')">
                                                <i class="el-icon-view"></i> 预览
                                            </el-button>
                                            <el-button size="small" type="success" @click="downloadFile(designData.renderingimages, '效果图.jpg')">
                                                <i class="el-icon-download"></i> 下载
                                            </el-button>
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </el-tab-pane>
                
                <!-- 材料信息 -->
                <el-tab-pane label="材料信息" name="materials">
                    <div class="materials-content">
                        <el-descriptions :column="1" border>
                            <el-descriptions-item label="主要材料">{{designData.mainmaterials}}</el-descriptions-item>
                            <el-descriptions-item label="外墙材料">{{designData.wallmaterials}}</el-descriptions-item>
                            <el-descriptions-item label="屋顶材料">{{designData.roofmaterials}}</el-descriptions-item>
                            <el-descriptions-item label="门窗材料">{{designData.windowmaterials}}</el-descriptions-item>
                        </el-descriptions>
                    </div>
                </el-tab-pane>
                
                <!-- 设计文件 -->
                <el-tab-pane label="设计文件" name="files">
                    <div class="files-content">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="file-item">
                                    <div class="file-header">
                                        <h4><i class="fas fa-file-pdf"></i> 设计图纸</h4>
                                    </div>
                                    <div class="file-content">
                                        <p v-if="!designData.designdrawings" class="no-file">暂无文件</p>
                                        <div v-else class="file-info">
                                            <span>{{designData.designdrawings}}</span>
                                            <div class="file-actions">
                                                <el-button size="mini" type="primary" @click="previewFile(designData.designdrawings, 'file')">预览</el-button>
                                                <el-button size="mini" type="success" @click="downloadFile(designData.designdrawings, '设计图纸')">下载</el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="file-item">
                                    <div class="file-header">
                                        <h4><i class="fas fa-cube"></i> 3D模型文件</h4>
                                    </div>
                                    <div class="file-content">
                                        <p v-if="!designData.model3dfile" class="no-file">暂无文件</p>
                                        <div v-else class="file-info">
                                            <span>{{designData.model3dfile}}</span>
                                            <div class="file-actions">
                                                <el-button size="mini" type="primary" @click="previewFile(designData.model3dfile, 'file')">预览</el-button>
                                                <el-button size="mini" type="success" @click="downloadFile(designData.model3dfile, '3D模型')">下载</el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="file-item">
                                    <div class="file-header">
                                        <h4><i class="fas fa-drafting-compass"></i> CAD文件</h4>
                                    </div>
                                    <div class="file-content">
                                        <p v-if="!designData.cadfile" class="no-file">暂无文件</p>
                                        <div v-else class="file-info">
                                            <span>{{designData.cadfile}}</span>
                                            <div class="file-actions">
                                                <el-button size="mini" type="primary" @click="previewFile(designData.cadfile, 'file')">预览</el-button>
                                                <el-button size="mini" type="success" @click="downloadFile(designData.cadfile, 'CAD文件')">下载</el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="file-item">
                                    <div class="file-header">
                                        <h4><i class="fas fa-image"></i> 效果图</h4>
                                    </div>
                                    <div class="file-content">
                                        <p v-if="!designData.renderingimages" class="no-file">暂无文件</p>
                                        <div v-else class="file-info">
                                            <img :src="designData.renderingimages" style="width: 100%; max-height: 150px; object-fit: cover; border-radius: 4px;">
                                            <div class="file-actions">
                                                <el-button size="mini" type="primary" @click="previewFile(designData.renderingimages, 'image')">预览</el-button>
                                                <el-button size="mini" type="success" @click="downloadFile(designData.renderingimages, '效果图')">下载</el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </el-tab-pane>
                
                <!-- 设计说明 -->
                <el-tab-pane label="设计说明" name="description">
                    <div class="description-content">
                        <div class="description-text">
                            <p v-if="!designData.designdescription" class="no-content">暂无设计说明</p>
                            <div v-else v-html="designData.designdescription.replace(/\\n/g, '<br>')"></div>
                        </div>
                    </div>
                </el-tab-pane>
                
                <!-- 审核记录 -->
                <el-tab-pane label="审核记录" name="audit">
                    <div class="audit-content">
                        <el-descriptions :column="1" border>
                            <el-descriptions-item label="审核状态">
                                <el-tag :type="statusType">{{designData.auditstatus}}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="审核意见">
                                <p v-if="!designData.auditcomments" class="no-content">暂无审核意见</p>
                                <div v-else>{{designData.auditcomments}}</div>
                            </el-descriptions-item>
                        </el-descriptions>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
    `,
    
    style: `
        <style scoped>
        .design-detail {
            padding: 20px;
        }
        
        .detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #ebeef5;
        }
        
        .header-left h2 {
            margin: 0 0 5px 0;
            color: #2c3e50;
            font-size: 24px;
        }
        
        .design-code {
            margin: 0;
            color: #909399;
            font-size: 14px;
        }
        
        .header-right {
            text-align: right;
        }
        
        .action-buttons {
            margin-top: 10px;
        }
        
        .action-buttons .el-button {
            margin-left: 10px;
        }
        
        .info-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            display: flex;
            align-items: center;
        }
        
        .info-card:nth-child(2) {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .info-card:nth-child(3) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .info-card:nth-child(4) {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .card-icon {
            font-size: 30px;
            margin-right: 15px;
            opacity: 0.8;
        }
        
        .card-content h4 {
            margin: 0 0 5px 0;
            font-size: 20px;
            font-weight: 600;
        }
        
        .card-content p {
            margin: 0;
            font-size: 12px;
            opacity: 0.9;
        }
        
        .overview-content .basic-info,
        .overview-content .parameters-info {
            margin-bottom: 30px;
        }
        
        .overview-content h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
        }
        
        .rendering-preview .image-container {
            position: relative;
            border: 1px solid #ebeef5;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .rendering-preview img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            cursor: pointer;
        }
        
        .image-actions {
            padding: 10px;
            text-align: center;
            background: #f8f9fa;
        }
        
        .file-item {
            border: 1px solid #ebeef5;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .file-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ebeef5;
        }
        
        .file-header h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 16px;
        }
        
        .file-content {
            padding: 15px;
        }
        
        .file-info {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .file-actions {
            display: flex;
            gap: 10px;
        }
        
        .no-file,
        .no-content {
            color: #909399;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }
        
        .description-text {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            line-height: 1.6;
        }
        </style>
    `
});
