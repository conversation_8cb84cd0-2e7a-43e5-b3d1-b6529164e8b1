<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd">

    <mvc:default-servlet-handler/>

    <!-- 自动注入 -->
    <context:component-scan base-package="com.cl"/>

	<mvc:annotation-driven>
    </mvc:annotation-driven>

    <!-- 静态资源 -->
    <mvc:resources mapping="/resources/**" location="/resources/"/>
    
    <!-- 拦截器 -->
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <mvc:exclude-mapping path="/upload"/>
            <bean class="com.cl.interceptor.AuthorizationInterceptor"/>
        </mvc:interceptor>
    </mvc:interceptors>

    <!-- 上传限制 -->
    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <!-- 文件大小限制为100M -->
        <property name="maxUploadSize" value="104857600"/>
    </bean>

</beans>
