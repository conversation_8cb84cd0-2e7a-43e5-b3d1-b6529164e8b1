-- 建筑工程项目管理系统增强版数据库结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS `cl3841596_enhanced` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `cl3841596_enhanced`;

-- 建筑设计表
CREATE TABLE `jianzhudesign` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `designcode` varchar(50) NOT NULL COMMENT '设计编号',
  `designname` varchar(200) NOT NULL COMMENT '设计名称',
  `xiang<PERSON><PERSON>hao` varchar(50) DEFAULT NULL COMMENT '项目编号',
  `buildingtype` varchar(50) DEFAULT NULL COMMENT '建筑类型',
  `architecturalstyle` varchar(50) DEFAULT NULL COMMENT '建筑风格',
  `buildingarea` decimal(10,2) DEFAULT NULL COMMENT '建筑面积',
  `buildingheight` decimal(8,2) DEFAULT NULL COMMENT '建筑高度',
  `floors` int(11) DEFAULT NULL COMMENT '建筑层数',
  `basementfloors` int(11) DEFAULT 0 COMMENT '地下层数',
  `structuretype` varchar(50) DEFAULT NULL COMMENT '结构类型',
  `mainmaterials` text COMMENT '主要材料',
  `wallmaterials` varchar(200) DEFAULT NULL COMMENT '外墙材料',
  `roofmaterials` varchar(200) DEFAULT NULL COMMENT '屋顶材料',
  `windowmaterials` varchar(200) DEFAULT NULL COMMENT '门窗材料',
  `designdrawings` varchar(500) DEFAULT NULL COMMENT '设计图纸',
  `model3dfile` varchar(500) DEFAULT NULL COMMENT '3D模型文件',
  `cadfile` varchar(500) DEFAULT NULL COMMENT 'CAD文件',
  `renderingimages` varchar(500) DEFAULT NULL COMMENT '效果图',
  `designdescription` text COMMENT '设计说明',
  `designer` varchar(100) DEFAULT NULL COMMENT '设计师',
  `designdate` date DEFAULT NULL COMMENT '设计日期',
  `auditstatus` varchar(20) DEFAULT '待审核' COMMENT '审核状态',
  `auditcomments` text COMMENT '审核意见',
  `budgetamount` decimal(15,2) DEFAULT NULL COMMENT '预算金额',
  `designversion` varchar(20) DEFAULT '1.0' COMMENT '设计版本',
  `isenabled` varchar(10) DEFAULT '是' COMMENT '是否启用',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `designcode` (`designcode`),
  KEY `idx_xiangmubianhao` (`xiangmubianhao`),
  KEY `idx_buildingtype` (`buildingtype`),
  KEY `idx_designer` (`designer`),
  KEY `idx_auditstatus` (`auditstatus`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='建筑设计表';

-- 建筑材料库表
CREATE TABLE `buildingmaterial` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `materialcode` varchar(50) NOT NULL COMMENT '材料编号',
  `materialname` varchar(200) NOT NULL COMMENT '材料名称',
  `materialcategory` varchar(50) DEFAULT NULL COMMENT '材料类别',
  `materialbrand` varchar(100) DEFAULT NULL COMMENT '材料品牌',
  `materialspec` varchar(200) DEFAULT NULL COMMENT '材料规格',
  `materialunit` varchar(20) DEFAULT NULL COMMENT '材料单位',
  `materialprice` decimal(10,2) DEFAULT NULL COMMENT '材料单价',
  `materialimage` varchar(500) DEFAULT NULL COMMENT '材料图片',
  `materialdescription` text COMMENT '材料描述',
  `supplier` varchar(200) DEFAULT NULL COMMENT '供应商',
  `suppliercontact` varchar(200) DEFAULT NULL COMMENT '供应商联系方式',
  `qualitygrade` varchar(20) DEFAULT NULL COMMENT '材料质量等级',
  `environmentalgrade` varchar(20) DEFAULT NULL COMMENT '环保等级',
  `firegrade` varchar(20) DEFAULT NULL COMMENT '防火等级',
  `servicelife` int(11) DEFAULT NULL COMMENT '使用寿命(年)',
  `maintenancecycle` int(11) DEFAULT NULL COMMENT '维护周期(月)',
  `applicableparts` varchar(200) DEFAULT NULL COMMENT '适用部位',
  `technicalparameters` text COMMENT '技术参数',
  `installationrequirements` text COMMENT '安装要求',
  `stockquantity` int(11) DEFAULT 0 COMMENT '库存数量',
  `minstock` int(11) DEFAULT 0 COMMENT '最小库存',
  `isenabled` varchar(10) DEFAULT '是' COMMENT '是否启用',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `materialcode` (`materialcode`),
  KEY `idx_materialcategory` (`materialcategory`),
  KEY `idx_materialbrand` (`materialbrand`),
  KEY `idx_supplier` (`supplier`),
  KEY `idx_stockquantity` (`stockquantity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='建筑材料库表';

-- 项目进度管理表
CREATE TABLE `projectprogress` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `progresscode` varchar(50) NOT NULL COMMENT '进度编号',
  `xiangmubianhao` varchar(50) DEFAULT NULL COMMENT '项目编号',
  `xiangmumingcheng` varchar(200) DEFAULT NULL COMMENT '项目名称',
  `stagename` varchar(100) NOT NULL COMMENT '阶段名称',
  `stagedescription` text COMMENT '阶段描述',
  `plannedstartdate` date DEFAULT NULL COMMENT '计划开始时间',
  `plannedenddate` date DEFAULT NULL COMMENT '计划结束时间',
  `actualstartdate` date DEFAULT NULL COMMENT '实际开始时间',
  `actualenddate` date DEFAULT NULL COMMENT '实际结束时间',
  `plannedprogress` decimal(5,2) DEFAULT NULL COMMENT '计划进度(%)',
  `actualprogress` decimal(5,2) DEFAULT NULL COMMENT '实际进度(%)',
  `progressstatus` varchar(20) DEFAULT '未开始' COMMENT '进度状态',
  `responsibleperson` varchar(100) DEFAULT NULL COMMENT '负责人',
  `participants` text COMMENT '参与人员',
  `workcontent` text COMMENT '工作内容',
  `completionstatus` text COMMENT '完成情况',
  `issues` text COMMENT '存在问题',
  `solutions` text COMMENT '解决方案',
  `riskassessment` text COMMENT '风险评估',
  `qualityevaluation` text COMMENT '质量评价',
  `budgetcost` decimal(15,2) DEFAULT NULL COMMENT '成本预算',
  `actualcost` decimal(15,2) DEFAULT NULL COMMENT '实际成本',
  `costvariance` decimal(15,2) DEFAULT NULL COMMENT '成本差异',
  `milestoneflag` varchar(10) DEFAULT '否' COMMENT '里程碑标记',
  `criticalpath` varchar(10) DEFAULT '否' COMMENT '关键路径',
  `dependencies` text COMMENT '依赖关系',
  `resourcerequirements` text COMMENT '资源需求',
  `remarks` text COMMENT '备注说明',
  `updatetime` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updatedby` varchar(100) DEFAULT NULL COMMENT '更新人',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `progresscode` (`progresscode`),
  KEY `idx_xiangmubianhao` (`xiangmubianhao`),
  KEY `idx_stagename` (`stagename`),
  KEY `idx_progressstatus` (`progressstatus`),
  KEY `idx_responsibleperson` (`responsibleperson`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目进度管理表';

-- 质量监控表
CREATE TABLE `qualitymonitor` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `checkcode` varchar(50) NOT NULL COMMENT '检查编号',
  `xiangmubianhao` varchar(50) DEFAULT NULL COMMENT '项目编号',
  `xiangmumingcheng` varchar(200) DEFAULT NULL COMMENT '项目名称',
  `checktype` varchar(50) DEFAULT NULL COMMENT '检查类型',
  `checkstage` varchar(50) DEFAULT NULL COMMENT '检查阶段',
  `checkpart` varchar(100) DEFAULT NULL COMMENT '检查部位',
  `checkcontent` text COMMENT '检查内容',
  `checkstandard` text COMMENT '检查标准',
  `checkmethod` varchar(200) DEFAULT NULL COMMENT '检查方法',
  `checkdate` date DEFAULT NULL COMMENT '检查日期',
  `checkperson` varchar(100) DEFAULT NULL COMMENT '检查人员',
  `accompanyperson` varchar(200) DEFAULT NULL COMMENT '陪同人员',
  `checkresult` varchar(50) DEFAULT NULL COMMENT '检查结果',
  `qualitygrade` varchar(20) DEFAULT NULL COMMENT '质量等级',
  `passrate` decimal(5,2) DEFAULT NULL COMMENT '合格率(%)',
  `foundissues` text COMMENT '发现问题',
  `issuelevel` varchar(20) DEFAULT NULL COMMENT '问题等级',
  `rectificationrequirements` text COMMENT '整改要求',
  `rectificationdeadline` date DEFAULT NULL COMMENT '整改期限',
  `rectificationperson` varchar(100) DEFAULT NULL COMMENT '整改负责人',
  `rectificationstatus` varchar(50) DEFAULT NULL COMMENT '整改情况',
  `recheckdate` date DEFAULT NULL COMMENT '复查日期',
  `recheckresult` varchar(50) DEFAULT NULL COMMENT '复查结果',
  `recheckperson` varchar(100) DEFAULT NULL COMMENT '复查人员',
  `checkphotos` text COMMENT '检查照片',
  `checkreport` varchar(500) DEFAULT NULL COMMENT '检查报告',
  `rectificationphotos` text COMMENT '整改照片',
  `penaltymeasures` text COMMENT '处罚措施',
  `penaltyamount` decimal(10,2) DEFAULT NULL COMMENT '处罚金额',
  `rewardmeasures` text COMMENT '奖励措施',
  `rewardamount` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `experiencesummary` text COMMENT '经验总结',
  `improvementsuggestions` text COMMENT '改进建议',
  `supervisoropinion` text COMMENT '监理意见',
  `owneropinion` text COMMENT '业主意见',
  `status` varchar(20) DEFAULT '进行中' COMMENT '状态',
  `remarks` text COMMENT '备注',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `checkcode` (`checkcode`),
  KEY `idx_xiangmubianhao` (`xiangmubianhao`),
  KEY `idx_checktype` (`checktype`),
  KEY `idx_checkdate` (`checkdate`),
  KEY `idx_checkperson` (`checkperson`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='质量监控表';

-- 增强原有项目信息表
ALTER TABLE `xiangmuxinxi` 
ADD COLUMN `jianzhutype` varchar(50) DEFAULT NULL COMMENT '建筑类型' AFTER `jinglixingming`,
ADD COLUMN `jianzhufarea` decimal(10,2) DEFAULT NULL COMMENT '建筑面积' AFTER `jianzhutype`,
ADD COLUMN `jianzhuheight` decimal(8,2) DEFAULT NULL COMMENT '建筑高度' AFTER `jianzhufarea`,
ADD COLUMN `jianzhufloors` int(11) DEFAULT NULL COMMENT '建筑层数' AFTER `jianzhuheight`,
ADD COLUMN `jianzhustyle` varchar(50) DEFAULT NULL COMMENT '建筑风格' AFTER `jianzhufloors`,
ADD COLUMN `designdrawings` varchar(500) DEFAULT NULL COMMENT '设计图纸' AFTER `jianzhustyle`,
ADD COLUMN `model3dfile` varchar(500) DEFAULT NULL COMMENT '3D模型文件' AFTER `designdrawings`,
ADD COLUMN `buildingmaterials` text COMMENT '建筑材料' AFTER `model3dfile`,
ADD COLUMN `budgetamount` decimal(15,2) DEFAULT NULL COMMENT '预算金额' AFTER `buildingmaterials`,
ADD COLUMN `projectstatus` varchar(20) DEFAULT '规划中' COMMENT '项目状态' AFTER `budgetamount`,
ADD COLUMN `wangongriqi` date DEFAULT NULL COMMENT '完工日期' AFTER `projectstatus`;

-- 创建索引
ALTER TABLE `xiangmuxinxi` 
ADD INDEX `idx_jianzhutype` (`jianzhutype`),
ADD INDEX `idx_projectstatus` (`projectstatus`),
ADD INDEX `idx_budgetamount` (`budgetamount`);

-- 插入示例数据
INSERT INTO `jianzhudesign` VALUES 
(1, 'SJ20240125001', '绿城花园住宅设计', 'XM001', '住宅建筑', '现代简约', 15000.00, 45.50, 15, 2, '框架结构', '钢筋混凝土,钢材,玻璃', '外墙涂料', '防水卷材', '铝合金门窗', '/uploads/drawings/design001.pdf', '/uploads/models/model001.3ds', '/uploads/cad/cad001.dwg', '/uploads/images/render001.jpg', '现代简约风格住宅设计，注重环保和节能', '张建筑', '2024-01-20', '已通过', '设计方案合理，符合规范要求', 8500000.00, '1.0', '是', '2024-01-25 10:00:00'),
(2, 'SJ20240125002', '商业综合体设计', 'XM002', '商业建筑', '现代风格', 25000.00, 80.00, 20, 3, '钢结构', '钢材,玻璃幕墙,铝板', '玻璃幕墙', '钢结构屋面', '玻璃幕墙', '/uploads/drawings/design002.pdf', '/uploads/models/model002.3ds', '/uploads/cad/cad002.dwg', '/uploads/images/render002.jpg', '大型商业综合体，集购物、办公、娱乐于一体', '李设计', '2024-01-22', '待审核', '', 15000000.00, '1.0', '是', '2024-01-25 11:00:00');

INSERT INTO `buildingmaterial` VALUES 
(1, 'CL001', 'HRB400钢筋', '结构材料', '沙钢', 'Φ12-32mm', '吨', 4200.00, '/uploads/materials/steel001.jpg', '热轧带肋钢筋，强度等级HRB400', '江苏沙钢集团', '0512-58888888', 'A级', 'E1', 'A级', 50, 12, '主体结构', '屈服强度≥400MPa，抗拉强度≥540MPa', '按规范要求施工', 500, 50, '是', '2024-01-25 09:00:00'),
(2, 'CL002', 'C30混凝土', '结构材料', '海螺水泥', 'C30', '立方米', 350.00, '/uploads/materials/concrete001.jpg', '商品混凝土，强度等级C30', '海螺水泥股份有限公司', '0553-5555555', 'A级', 'E0', 'A级', 50, 6, '主体结构', '抗压强度≥30MPa', '按规范要求浇筑养护', 1000, 100, '是', '2024-01-25 09:30:00');

INSERT INTO `projectprogress` VALUES 
(1, 'JD20240125001', 'XM001', '绿城花园住宅小区', '基础施工', '进行桩基础和地下室施工', '2024-02-01', '2024-03-15', '2024-02-01', NULL, 100.00, 65.00, '进行中', '王工程师', '张三,李四,王五', '桩基础施工、地下室开挖、基础浇筑', '桩基础已完成80%，地下室开挖完成50%', '雨季影响施工进度', '增加排水措施，调整施工计划', '中等风险，需要关注天气变化', '质量良好，符合设计要求', 2000000.00, 1300000.00, -700000.00, '是', '是', '依赖地质勘探报告', '挖掘机2台，混凝土泵车1台', '注意安全施工', '2024-01-25 15:30:00', '王工程师', '2024-01-25 12:00:00');

INSERT INTO `qualitymonitor` VALUES 
(1, 'QC20240125001', 'XM001', '绿城花园住宅小区', '隐蔽工程检查', '基础施工阶段', '桩基础', '桩基础钢筋绑扎质量检查', 'GB50010-2010混凝土结构设计规范', '目测+量测', '2024-01-24', '质检员张三', '项目经理李四,监理工程师王五', '合格', '优良', 95.50, '个别钢筋间距略有偏差', '轻微', '调整钢筋间距至规范要求', '2024-01-25', '钢筋工班长', '已整改完成', '2024-01-25', '合格', '质检员张三', '/uploads/photos/check001.jpg', '/uploads/reports/report001.pdf', '/uploads/photos/rectify001.jpg', '', 0.00, '质量优良奖', 1000.00, '钢筋工程质量控制要点总结', '加强过程控制，提高一次合格率', '质量控制到位', '满意', '已完成', '继续保持质量标准', '2024-01-25 14:00:00');
