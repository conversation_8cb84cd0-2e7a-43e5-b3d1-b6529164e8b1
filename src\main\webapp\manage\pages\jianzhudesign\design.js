// 建筑设计管理主页面逻辑
new Vue({
    el: '#app',
    data() {
        return {
            // 基础数据
            loading: false,
            designList: [],
            selectedDesigns: [],
            currentDesign: {},
            
            // 分页数据
            currentPage: 1,
            pageSize: 10,
            total: 0,
            
            // 搜索筛选
            searchForm: {
                keyword: '',
                buildingtype: '',
                auditstatus: '',
                designer: '',
                dateRange: []
            },
            
            // 视图模式
            viewMode: 'table',
            
            // 对话框状态
            dialogVisible: false,
            detailVisible: false,
            isEdit: false,
            
            // 统计数据
            totalDesigns: 0,
            pendingDesigns: 0,
            approvedDesigns: 0,
            totalBudget: 0,
            
            // 设计师列表
            designerList: []
        }
    },
    
    computed: {
        dialogTitle() {
            return this.isEdit ? '编辑设计' : '新建设计';
        }
    },
    
    mounted() {
        this.loadData();
        this.loadStats();
        this.loadDesigners();
    },
    
    methods: {
        // 加载数据
        async loadData() {
            this.loading = true;
            try {
                const params = {
                    page: this.currentPage,
                    limit: this.pageSize,
                    ...this.searchForm
                };
                
                if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
                    params.startDate = this.formatDate(this.searchForm.dateRange[0]);
                    params.endDate = this.formatDate(this.searchForm.dateRange[1]);
                }
                
                const response = await axios.get('/jianzhudesign/page', { params });
                
                if (response.data.code === 0) {
                    this.designList = response.data.data.list || [];
                    this.total = response.data.data.total || 0;
                } else {
                    this.$message.error('加载数据失败');
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                this.$message.error('网络错误，请稍后重试');
            } finally {
                this.loading = false;
            }
        },
        
        // 加载统计数据
        async loadStats() {
            try {
                // 模拟统计数据，实际应该从后端获取
                this.totalDesigns = 156;
                this.pendingDesigns = 23;
                this.approvedDesigns = 128;
                this.totalBudget = 25680000;
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        },
        
        // 加载设计师列表
        async loadDesigners() {
            try {
                // 模拟设计师数据，实际应该从后端获取
                this.designerList = ['张建筑', '李设计', '王工程', '赵规划', '陈结构'];
            } catch (error) {
                console.error('加载设计师列表失败:', error);
            }
        },
        
        // 搜索处理
        handleSearch() {
            this.currentPage = 1;
            this.loadData();
        },
        
        // 分页处理
        handleSizeChange(val) {
            this.pageSize = val;
            this.loadData();
        },
        
        handleCurrentChange(val) {
            this.currentPage = val;
            this.loadData();
        },
        
        // 选择处理
        handleSelectionChange(val) {
            this.selectedDesigns = val;
        },
        
        // 视图模式切换
        changeViewMode(mode) {
            this.viewMode = mode;
        },
        
        // 新建设计
        showAddDialog() {
            this.currentDesign = {
                designcode: this.generateDesignCode(),
                designdate: new Date(),
                auditstatus: '待审核',
                isenabled: '是'
            };
            this.isEdit = false;
            this.dialogVisible = true;
        },
        
        // 编辑设计
        editDesign(design) {
            this.currentDesign = { ...design };
            this.isEdit = true;
            this.dialogVisible = true;
        },
        
        // 查看设计详情
        viewDesign(design) {
            this.currentDesign = { ...design };
            this.detailVisible = true;
        },
        
        // 审核设计
        async auditDesign(design) {
            this.$prompt('请输入审核意见', '设计审核', {
                confirmButtonText: '通过',
                cancelButtonText: '拒绝',
                inputPlaceholder: '审核意见...',
                showCancelButton: true,
                distinguishCancelAndClose: true
            }).then(async ({ value, action }) => {
                try {
                    const auditData = {
                        id: design.id,
                        auditstatus: action === 'confirm' ? '已通过' : '已拒绝',
                        auditcomments: value || ''
                    };
                    
                    const response = await axios.post('/jianzhudesign/audit', auditData);
                    
                    if (response.data.code === 0) {
                        this.$message.success('审核完成');
                        this.loadData();
                        this.loadStats();
                    } else {
                        this.$message.error('审核失败');
                    }
                } catch (error) {
                    console.error('审核失败:', error);
                    this.$message.error('网络错误，请稍后重试');
                }
            }).catch(() => {
                // 用户取消
            });
        },
        
        // 删除设计
        deleteDesign(design) {
            this.$confirm('确定要删除这个设计吗？', '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    const response = await axios.post('/jianzhudesign/delete', [design.id]);
                    
                    if (response.data.code === 0) {
                        this.$message.success('删除成功');
                        this.loadData();
                        this.loadStats();
                    } else {
                        this.$message.error('删除失败');
                    }
                } catch (error) {
                    console.error('删除失败:', error);
                    this.$message.error('网络错误，请稍后重试');
                }
            });
        },
        
        // 表单提交处理
        async handleSubmit(formData) {
            try {
                const url = this.isEdit ? '/jianzhudesign/update' : '/jianzhudesign/save';
                const response = await axios.post(url, formData);
                
                if (response.data.code === 0) {
                    this.$message.success(this.isEdit ? '更新成功' : '创建成功');
                    this.dialogVisible = false;
                    this.loadData();
                    this.loadStats();
                } else {
                    this.$message.error(response.data.msg || '操作失败');
                }
            } catch (error) {
                console.error('提交失败:', error);
                this.$message.error('网络错误，请稍后重试');
            }
        },
        
        // 对话框关闭处理
        handleClose() {
            this.dialogVisible = false;
            this.currentDesign = {};
        },
        
        closeDetail() {
            this.detailVisible = false;
            this.currentDesign = {};
        },
        
        // 导入对话框
        showImportDialog() {
            this.$message.info('批量导入功能开发中...');
        },
        
        // 导出数据
        exportData() {
            this.$message.info('数据导出功能开发中...');
        },
        
        // 工具方法
        generateDesignCode() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            return `SJ${year}${month}${day}${random}`;
        },
        
        formatDate(date) {
            if (!date) return '';
            const d = new Date(date);
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },
        
        formatMoney(amount) {
            if (!amount) return '0';
            return Number(amount).toLocaleString();
        },
        
        getStatusType(status) {
            switch (status) {
                case '已通过':
                    return 'success';
                case '待审核':
                    return 'warning';
                case '已拒绝':
                    return 'danger';
                default:
                    return 'info';
            }
        }
    }
});
