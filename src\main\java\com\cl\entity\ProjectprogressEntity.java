package com.cl.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.lang.reflect.InvocationTargetException;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.beanutils.BeanUtils;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.enums.FieldFill;
import com.baomidou.mybatisplus.enums.IdType;

/**
 * 项目进度管理
 * 数据库通用操作实体类（普通增删改查）
 * <AUTHOR> @email 
 * @date 2024-01-25 13:22:32
 */
@TableName("projectprogress")
public class ProjectprogressEntity<T> implements Serializable {
	private static final long serialVersionUID = 1L;

	public ProjectprogressEntity() {
		
	}
	
	public ProjectprogressEntity(T t) {
		try {
			BeanUtils.copyProperties(this, t);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	/**
	 * 主键id
	 */
	@TableId
	private Long id;
	
	/**
	 * 进度编号
	 */
	private String progresscode;
	
	/**
	 * 项目编号
	 */
	private String xiangmubianhao;
	
	/**
	 * 项目名称
	 */
	private String xiangmumingcheng;
	
	/**
	 * 阶段名称
	 */
	private String stagename;
	
	/**
	 * 阶段描述
	 */
	private String stagedescription;
	
	/**
	 * 计划开始时间
	 */
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat 		
	private Date plannedstartdate;
	
	/**
	 * 计划结束时间
	 */
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat 		
	private Date plannedenddate;
	
	/**
	 * 实际开始时间
	 */
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat 		
	private Date actualstartdate;
	
	/**
	 * 实际结束时间
	 */
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat 		
	private Date actualenddate;
	
	/**
	 * 计划进度
	 */
	private Double plannedprogress;
	
	/**
	 * 实际进度
	 */
	private Double actualprogress;
	
	/**
	 * 进度状态
	 */
	private String progressstatus;
	
	/**
	 * 负责人
	 */
	private String responsibleperson;
	
	/**
	 * 参与人员
	 */
	private String participants;
	
	/**
	 * 工作内容
	 */
	private String workcontent;
	
	/**
	 * 完成情况
	 */
	private String completionstatus;
	
	/**
	 * 存在问题
	 */
	private String issues;
	
	/**
	 * 解决方案
	 */
	private String solutions;
	
	/**
	 * 风险评估
	 */
	private String riskassessment;
	
	/**
	 * 质量评价
	 */
	private String qualityevaluation;
	
	/**
	 * 成本预算
	 */
	private Double budgetcost;
	
	/**
	 * 实际成本
	 */
	private Double actualcost;
	
	/**
	 * 成本差异
	 */
	private Double costvariance;
	
	/**
	 * 里程碑标记
	 */
	private String milestoneflag;
	
	/**
	 * 关键路径
	 */
	private String criticalpath;
	
	/**
	 * 依赖关系
	 */
	private String dependencies;
	
	/**
	 * 资源需求
	 */
	private String resourcerequirements;
	
	/**
	 * 备注说明
	 */
	private String remarks;
	
	/**
	 * 更新时间
	 */
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat 		
	private Date updatetime;
	
	/**
	 * 更新人
	 */
	private String updatedby;

	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat
	private Date addtime;

	public Date getAddtime() {
		return addtime;
	}
	public void setAddtime(Date addtime) {
		this.addtime = addtime;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	/**
	 * 设置：进度编号
	 */
	public void setProgresscode(String progresscode) {
		this.progresscode = progresscode;
	}
	/**
	 * 获取：进度编号
	 */
	public String getProgresscode() {
		return progresscode;
	}
	
	/**
	 * 设置：项目编号
	 */
	public void setXiangmubianhao(String xiangmubianhao) {
		this.xiangmubianhao = xiangmubianhao;
	}
	/**
	 * 获取：项目编号
	 */
	public String getXiangmubianhao() {
		return xiangmubianhao;
	}
	
	/**
	 * 设置：项目名称
	 */
	public void setXiangmumingcheng(String xiangmumingcheng) {
		this.xiangmumingcheng = xiangmumingcheng;
	}
	/**
	 * 获取：项目名称
	 */
	public String getXiangmumingcheng() {
		return xiangmumingcheng;
	}
	
	/**
	 * 设置：阶段名称
	 */
	public void setStagename(String stagename) {
		this.stagename = stagename;
	}
	/**
	 * 获取：阶段名称
	 */
	public String getStagename() {
		return stagename;
	}
	
	/**
	 * 设置：阶段描述
	 */
	public void setStagedescription(String stagedescription) {
		this.stagedescription = stagedescription;
	}
	/**
	 * 获取：阶段描述
	 */
	public String getStagedescription() {
		return stagedescription;
	}
	
	/**
	 * 设置：计划开始时间
	 */
	public void setPlannedstartdate(Date plannedstartdate) {
		this.plannedstartdate = plannedstartdate;
	}
	/**
	 * 获取：计划开始时间
	 */
	public Date getPlannedstartdate() {
		return plannedstartdate;
	}
	
	/**
	 * 设置：计划结束时间
	 */
	public void setPlannedenddate(Date plannedenddate) {
		this.plannedenddate = plannedenddate;
	}
	/**
	 * 获取：计划结束时间
	 */
	public Date getPlannedenddate() {
		return plannedenddate;
	}
	
	/**
	 * 设置：实际开始时间
	 */
	public void setActualstartdate(Date actualstartdate) {
		this.actualstartdate = actualstartdate;
	}
	/**
	 * 获取：实际开始时间
	 */
	public Date getActualstartdate() {
		return actualstartdate;
	}
	
	/**
	 * 设置：实际结束时间
	 */
	public void setActualenddate(Date actualenddate) {
		this.actualenddate = actualenddate;
	}
	/**
	 * 获取：实际结束时间
	 */
	public Date getActualenddate() {
		return actualenddate;
	}
	
	/**
	 * 设置：计划进度
	 */
	public void setPlannedprogress(Double plannedprogress) {
		this.plannedprogress = plannedprogress;
	}
	/**
	 * 获取：计划进度
	 */
	public Double getPlannedprogress() {
		return plannedprogress;
	}
	
	/**
	 * 设置：实际进度
	 */
	public void setActualprogress(Double actualprogress) {
		this.actualprogress = actualprogress;
	}
	/**
	 * 获取：实际进度
	 */
	public Double getActualprogress() {
		return actualprogress;
	}
	
	/**
	 * 设置：进度状态
	 */
	public void setProgressstatus(String progressstatus) {
		this.progressstatus = progressstatus;
	}
	/**
	 * 获取：进度状态
	 */
	public String getProgressstatus() {
		return progressstatus;
	}
	
	/**
	 * 设置：负责人
	 */
	public void setResponsibleperson(String responsibleperson) {
		this.responsibleperson = responsibleperson;
	}
	/**
	 * 获取：负责人
	 */
	public String getResponsibleperson() {
		return responsibleperson;
	}
	
	/**
	 * 设置：参与人员
	 */
	public void setParticipants(String participants) {
		this.participants = participants;
	}
	/**
	 * 获取：参与人员
	 */
	public String getParticipants() {
		return participants;
	}
	
	/**
	 * 设置：工作内容
	 */
	public void setWorkcontent(String workcontent) {
		this.workcontent = workcontent;
	}
	/**
	 * 获取：工作内容
	 */
	public String getWorkcontent() {
		return workcontent;
	}
	
	/**
	 * 设置：完成情况
	 */
	public void setCompletionstatus(String completionstatus) {
		this.completionstatus = completionstatus;
	}
	/**
	 * 获取：完成情况
	 */
	public String getCompletionstatus() {
		return completionstatus;
	}
	
	/**
	 * 设置：存在问题
	 */
	public void setIssues(String issues) {
		this.issues = issues;
	}
	/**
	 * 获取：存在问题
	 */
	public String getIssues() {
		return issues;
	}
	
	/**
	 * 设置：解决方案
	 */
	public void setSolutions(String solutions) {
		this.solutions = solutions;
	}
	/**
	 * 获取：解决方案
	 */
	public String getSolutions() {
		return solutions;
	}

	/**
	 * 设置：风险评估
	 */
	public void setRiskassessment(String riskassessment) {
		this.riskassessment = riskassessment;
	}
	/**
	 * 获取：风险评估
	 */
	public String getRiskassessment() {
		return riskassessment;
	}

	/**
	 * 设置：质量评价
	 */
	public void setQualityevaluation(String qualityevaluation) {
		this.qualityevaluation = qualityevaluation;
	}
	/**
	 * 获取：质量评价
	 */
	public String getQualityevaluation() {
		return qualityevaluation;
	}

	/**
	 * 设置：成本预算
	 */
	public void setBudgetcost(Double budgetcost) {
		this.budgetcost = budgetcost;
	}
	/**
	 * 获取：成本预算
	 */
	public Double getBudgetcost() {
		return budgetcost;
	}

	/**
	 * 设置：实际成本
	 */
	public void setActualcost(Double actualcost) {
		this.actualcost = actualcost;
	}
	/**
	 * 获取：实际成本
	 */
	public Double getActualcost() {
		return actualcost;
	}

	/**
	 * 设置：成本差异
	 */
	public void setCostvariance(Double costvariance) {
		this.costvariance = costvariance;
	}
	/**
	 * 获取：成本差异
	 */
	public Double getCostvariance() {
		return costvariance;
	}

	/**
	 * 设置：里程碑标记
	 */
	public void setMilestoneflag(String milestoneflag) {
		this.milestoneflag = milestoneflag;
	}
	/**
	 * 获取：里程碑标记
	 */
	public String getMilestoneflag() {
		return milestoneflag;
	}

	/**
	 * 设置：关键路径
	 */
	public void setCriticalpath(String criticalpath) {
		this.criticalpath = criticalpath;
	}
	/**
	 * 获取：关键路径
	 */
	public String getCriticalpath() {
		return criticalpath;
	}

	/**
	 * 设置：依赖关系
	 */
	public void setDependencies(String dependencies) {
		this.dependencies = dependencies;
	}
	/**
	 * 获取：依赖关系
	 */
	public String getDependencies() {
		return dependencies;
	}

	/**
	 * 设置：资源需求
	 */
	public void setResourcerequirements(String resourcerequirements) {
		this.resourcerequirements = resourcerequirements;
	}
	/**
	 * 获取：资源需求
	 */
	public String getResourcerequirements() {
		return resourcerequirements;
	}

	/**
	 * 设置：备注说明
	 */
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	/**
	 * 获取：备注说明
	 */
	public String getRemarks() {
		return remarks;
	}

	/**
	 * 设置：更新时间
	 */
	public void setUpdatetime(Date updatetime) {
		this.updatetime = updatetime;
	}
	/**
	 * 获取：更新时间
	 */
	public Date getUpdatetime() {
		return updatetime;
	}

	/**
	 * 设置：更新人
	 */
	public void setUpdatedby(String updatedby) {
		this.updatedby = updatedby;
	}
	/**
	 * 获取：更新人
	 */
	public String getUpdatedby() {
		return updatedby;
	}
}
