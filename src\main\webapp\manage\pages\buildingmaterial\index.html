<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>建筑材料库 - 建筑工程项目管理系统</title>
    <link rel="stylesheet" href="../../css/element-ui.css">
    <link rel="stylesheet" href="../../css/common.css">
    <link rel="stylesheet" href="./material.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="app">
        <div class="material-container">
            <!-- 顶部导航栏 -->
            <div class="top-nav">
                <div class="nav-left">
                    <h1><i class="fas fa-cubes"></i> 建筑材料库</h1>
                </div>
                <div class="nav-right">
                    <el-button type="primary" icon="el-icon-plus" @click="showAddDialog">新增材料</el-button>
                    <el-button type="success" icon="el-icon-upload2" @click="showImportDialog">批量导入</el-button>
                    <el-button type="warning" icon="el-icon-warning" @click="showStockAlert">库存预警</el-button>
                    <el-button type="info" icon="el-icon-download" @click="exportData">导出数据</el-button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-cards">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{totalMaterials}}</h3>
                        <p>材料总数</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{lowStockCount}}</h3>
                        <p>库存预警</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{categoryCount}}</h3>
                        <p>材料类别</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{supplierCount}}</h3>
                        <p>供应商数量</p>
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="search-filter">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-input
                            v-model="searchForm.keyword"
                            placeholder="搜索材料名称、编号..."
                            prefix-icon="el-icon-search"
                            @input="handleSearch">
                        </el-input>
                    </el-col>
                    <el-col :span="4">
                        <el-select v-model="searchForm.category" placeholder="材料类别" @change="handleSearch">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="结构材料" value="结构材料"></el-option>
                            <el-option label="装饰材料" value="装饰材料"></el-option>
                            <el-option label="防水材料" value="防水材料"></el-option>
                            <el-option label="保温材料" value="保温材料"></el-option>
                            <el-option label="门窗材料" value="门窗材料"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <el-select v-model="searchForm.brand" placeholder="材料品牌" @change="handleSearch">
                            <el-option label="全部" value=""></el-option>
                            <el-option v-for="brand in brandList" :key="brand" :label="brand" :value="brand"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <el-select v-model="searchForm.supplier" placeholder="供应商" @change="handleSearch">
                            <el-option label="全部" value=""></el-option>
                            <el-option v-for="supplier in supplierList" :key="supplier" :label="supplier" :value="supplier"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="3">
                        <el-select v-model="searchForm.stockStatus" placeholder="库存状态" @change="handleSearch">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="正常" value="normal"></el-option>
                            <el-option label="预警" value="warning"></el-option>
                            <el-option label="缺货" value="shortage"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="3">
                        <el-button type="primary" @click="handleSearch">搜索</el-button>
                    </el-col>
                </el-row>
            </div>

            <!-- 视图切换 -->
            <div class="view-toggle">
                <el-radio-group v-model="viewMode" @change="changeViewMode">
                    <el-radio-button label="table"><i class="el-icon-s-grid"></i> 表格视图</el-radio-button>
                    <el-radio-button label="card"><i class="el-icon-s-order"></i> 卡片视图</el-radio-button>
                    <el-radio-button label="gallery"><i class="el-icon-picture"></i> 图片视图</el-radio-button>
                </el-radio-group>
            </div>

            <!-- 表格视图 -->
            <div v-if="viewMode === 'table'" class="table-view">
                <el-table
                    :data="materialList"
                    style="width: 100%"
                    :loading="loading"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column prop="materialcode" label="材料编号" width="120"></el-table-column>
                    <el-table-column prop="materialname" label="材料名称" width="200" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="materialcategory" label="材料类别" width="100"></el-table-column>
                    <el-table-column prop="materialbrand" label="品牌" width="120"></el-table-column>
                    <el-table-column prop="materialspec" label="规格" width="150" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="materialunit" label="单位" width="80"></el-table-column>
                    <el-table-column prop="materialprice" label="单价" width="100">
                        <template slot-scope="scope">
                            ¥{{scope.row.materialprice}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="stockquantity" label="库存" width="100">
                        <template slot-scope="scope">
                            <span :class="getStockClass(scope.row)">{{scope.row.stockquantity}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="supplier" label="供应商" width="120"></el-table-column>
                    <el-table-column prop="qualitygrade" label="质量等级" width="100">
                        <template slot-scope="scope">
                            <el-tag :type="getGradeType(scope.row.qualitygrade)">{{scope.row.qualitygrade}}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200" fixed="right">
                        <template slot-scope="scope">
                            <el-button size="mini" @click="viewMaterial(scope.row)">查看</el-button>
                            <el-button size="mini" type="primary" @click="editMaterial(scope.row)">编辑</el-button>
                            <el-button size="mini" type="warning" @click="adjustStock(scope.row)">调库存</el-button>
                            <el-button size="mini" type="danger" @click="deleteMaterial(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 卡片视图 -->
            <div v-if="viewMode === 'card'" class="card-view">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="material in materialList" :key="material.id">
                        <div class="material-card">
                            <div class="card-header">
                                <h3>{{material.materialname}}</h3>
                                <el-tag :type="getGradeType(material.qualitygrade)">{{material.qualitygrade}}</el-tag>
                            </div>
                            <div class="card-image">
                                <img :src="material.materialimage || '/images/default-material.jpg'" alt="材料图片">
                                <div class="stock-badge" :class="getStockClass(material)">
                                    库存: {{material.stockquantity}}{{material.materialunit}}
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="material-info">
                                    <p><i class="fas fa-code"></i> {{material.materialcode}}</p>
                                    <p><i class="fas fa-tags"></i> {{material.materialcategory}}</p>
                                    <p><i class="fas fa-copyright"></i> {{material.materialbrand}}</p>
                                    <p><i class="fas fa-ruler"></i> {{material.materialspec}}</p>
                                    <p><i class="fas fa-dollar-sign"></i> ¥{{material.materialprice}}/{{material.materialunit}}</p>
                                    <p><i class="fas fa-truck"></i> {{material.supplier}}</p>
                                </div>
                            </div>
                            <div class="card-actions">
                                <el-button size="small" @click="viewMaterial(material)">详情</el-button>
                                <el-button size="small" type="primary" @click="editMaterial(material)">编辑</el-button>
                                <el-button size="small" type="warning" @click="adjustStock(material)">调库存</el-button>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[12, 24, 48, 96]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total">
                </el-pagination>
            </div>
        </div>

        <!-- 新建/编辑材料对话框 -->
        <el-dialog
            :title="dialogTitle"
            :visible.sync="dialogVisible"
            width="70%"
            :before-close="handleClose">
            <material-form
                ref="materialForm"
                :form-data="currentMaterial"
                :is-edit="isEdit"
                @submit="handleSubmit"
                @cancel="handleClose">
            </material-form>
        </el-dialog>

        <!-- 材料详情对话框 -->
        <el-dialog
            title="材料详情"
            :visible.sync="detailVisible"
            width="80%"
            :before-close="closeDetail">
            <material-detail
                :material-data="currentMaterial"
                @edit="editMaterial"
                @adjust-stock="adjustStock">
            </material-detail>
        </el-dialog>

        <!-- 库存调整对话框 -->
        <el-dialog
            title="库存调整"
            :visible.sync="stockDialogVisible"
            width="400px">
            <stock-adjust
                :material-data="currentMaterial"
                @submit="handleStockAdjust"
                @cancel="stockDialogVisible = false">
            </stock-adjust>
        </el-dialog>

        <!-- 库存预警对话框 -->
        <el-dialog
            title="库存预警"
            :visible.sync="alertDialogVisible"
            width="60%">
            <stock-alert
                :alert-list="alertList"
                @adjust="adjustStock">
            </stock-alert>
        </el-dialog>
    </div>

    <script src="../../js/vue.js"></script>
    <script src="../../js/element-ui.js"></script>
    <script src="../../js/axios.js"></script>
    <script src="./components/material-form.js"></script>
    <script src="./components/material-detail.js"></script>
    <script src="./components/stock-adjust.js"></script>
    <script src="./components/stock-alert.js"></script>
    <script src="./material.js"></script>
</body>
</html>
