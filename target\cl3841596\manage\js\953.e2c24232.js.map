{"version": 3, "file": "js/953.e2c24232.js", "mappings": "yuBAsCOA,GAAWC,EAAAA,EAAAA,IAAI,IACfC,GAAQD,EAAAA,EAAAA,IAAI,IACZE,GAAYF,EAAAA,EAAAA,IAAI,CACrBG,KAAM,GACNC,SAAU,GACVC,SAAU,KAELC,GAAYN,EAAAA,EAAAA,IAAI,IAChBO,GAAYP,EAAAA,EAAAA,IAAI,GAChBQ,EAA8B,QAAvBC,GAAGC,EAAAA,EAAAA,aAAoB,IAAAD,OAAA,EAApBA,EAAsBE,WAAWC,OAAOC,iBAElDC,EAAW,SAACX,GACjBD,EAAUa,MAAMZ,KAAOA,CAExB,EACMa,EAAc,WACnB,GAAKd,EAAUa,MAAMX,SAKrB,GAAKF,EAAUa,MAAMV,SAArB,CAKA,GAAIN,EAASgB,MAAME,OAAS,EAAG,CAC9B,IAAKf,EAAUa,MAAMZ,KAGpB,OAFO,OAAPK,QAAO,IAAPA,GAAAA,EAASU,UAAUC,QAAQ,QAAS,cACpCC,aAAaL,MAAMM,QAGpB,IAAK,IAAIC,EAAI,EAAGA,EAAIrB,EAAMc,MAAME,OAAQK,IACnCrB,EAAMc,MAAMO,GAAGC,UAAYrB,EAAUa,MAAMZ,OAC9CG,EAAUS,MAAQd,EAAMc,MAAMO,GAAGhB,UAGpC,MACCA,EAAUS,MAAQhB,EAASgB,MAAM,GAAGT,UACpCJ,EAAUa,MAAMZ,KAAOJ,EAASgB,MAAM,GAAGQ,SAE1CC,GAhBA,MAHQ,OAAPhB,QAAO,IAAPA,GAAAA,EAASU,UAAUC,QAAQ,QAAS,cAL7B,OAAPX,QAAO,IAAPA,GAAAA,EAASU,UAAUC,QAAQ,SAAU,QAyBvC,EACMK,EAAQ,WACN,OAAPhB,QAAO,IAAPA,GAAAA,EAASiB,MAAM,CACdC,IAAK,GAAFC,OAAKrB,EAAUS,MAAK,oBAAAY,OAAmBzB,EAAUa,MAAMX,SAAQ,cAAAuB,OAAazB,EAAUa,MAAMV,UAC/FuB,OAAQ,SACNC,MAAK,SAAAC,GACA,OAAPtB,QAAO,IAAPA,GAAAA,EAASU,UAAUa,WAAW,QAASD,EAAIE,KAAKC,OACzC,OAAPzB,QAAO,IAAPA,GAAAA,EAASU,UAAUa,WAAW,OAAQ7B,EAAUa,MAAMZ,MAC/C,OAAPK,QAAO,IAAPA,GAAAA,EAASU,UAAUa,WAAW,eAAgBzB,EAAUS,OACjD,OAAPP,QAAO,IAAPA,GAAAA,EAASU,UAAUa,WAAW,YAAa7B,EAAUa,MAAMX,UACpD,OAAPI,QAAO,IAAPA,GAAAA,EAAS0B,QAAQC,KAAK,IACvB,IAAG,SAAAC,GACH,GACD,EAEMC,EAAQ,WACT,IAAIC,EAAS,CACXC,KAAM,EACNC,MAAO,EACPC,KAAM,MAGD,OAAPjC,QAAO,IAAPA,GAAAA,EAASiB,MAAM,CACbC,IAAK,YACLE,OAAQ,MACRU,OAAQA,IACPT,MAAK,SAAAC,GACJ7B,EAAMc,MAAQ2B,KAAKC,MAAMb,EAAIE,KAAKA,KAAKY,KAAK,GAAGC,UAC/C,IAAK,IAAIvB,EAAI,EAAGA,EAAIrB,EAAMc,MAAME,OAAQK,IACL,KAA7BrB,EAAMc,MAAMO,GAAGwB,cACjB/C,EAASgB,MAAMoB,KAAKlC,EAAMc,MAAMO,IAG3CpB,EAAUa,MAAMZ,KAAOJ,EAASgB,MAAM,GAAGQ,SAC3B,OAAPf,QAAO,IAAPA,GAAAA,EAASU,UAAUa,WAAW,QAASW,KAAKK,UAAU9C,EAAMc,OAChE,GACF,EAEGiC,EAAO,WACZX,GACD,E,OACAY,EAAAA,EAAAA,KAAU,WACTD,GAED,I,0zCCtHD,MAAME,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,G", "sources": ["webpack://vue3_nb0/./src/views/login.vue", "webpack://vue3_nb0/./src/views/login.vue?379a"], "sourcesContent": ["<template>\r\n\t<div>\n\t\t<div class=\"login_view\">\r\n\t\t\t<div class=\"outTitle_view\">\r\n\t\t\t\t<div class=\"outTilte\">建筑工程项目管理系统登录</div>\r\n\t\t\t</div>\r\n\t\t\t<el-form :model=\"loginForm\" class=\"login_form\">\r\n\t\t\t\t<div class=\"tabView\" v-if=\"userList.length>1\">\r\n\t\t\t\t\t<div class=\"tab\" :style=\"{'width':`calc(100% / ${userList.length})`}\"\r\n\t\t\t\t\t\t:class=\"loginForm.role==item.roleName?'tabActive':''\" v-for=\"(item,index) in userList\"\r\n\t\t\t\t\t\t:key=\"index\" @click=\"tabClick(item.roleName)\">{{item.roleName}}</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"list_item\" v-if=\"loginType==1\">\r\n\t\t\t\t\t<div class=\"list_label\">\r\n\t\t\t\t\t\t账号：\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<input class=\"list_inp\" v-model=\"loginForm.username\" placeholder=\"请输入账号\" />\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"list_item\" v-if=\"loginType==1\">\r\n\t\t\t\t\t<div class=\"list_label\">\r\n\t\t\t\t\t\t密码：\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<input class=\"list_inp\" v-model=\"loginForm.password\" type=\"password\" placeholder=\"请输入密码\" @keydown.enter.native=\"handleLogin\"  />\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"btn_view\">\r\n\t\t\t\t\t<el-button class=\"login\" v-if=\"loginType==1\" type=\"success\" @click=\"handleLogin\">登录</el-button>\r\n\t\t\t\t</div>\r\n\t\t\t</el-form>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n<script setup>\r\n\timport {\r\n\t\tref,\r\n\t\tgetCurrentInstance,\r\n\t\tnextTick,\n\t\tonMounted,\r\n\t} from \"vue\";\r\n\tconst userList = ref([])\r\n\tconst menus = ref([])\r\n\tconst loginForm = ref({\r\n\t\trole: '',\r\n\t\tusername: '',\r\n\t\tpassword: ''\r\n\t})\r\n\tconst tableName = ref('')\r\n\tconst loginType = ref(1)\r\n\tconst context = getCurrentInstance()?.appContext.config.globalProperties;\r\n\t//登录用户tab切换\r\n\tconst tabClick = (role) => {\r\n\t\tloginForm.value.role = role\r\n\t\t\r\n\t}\r\n\tconst handleLogin = () => {\r\n\t\tif (!loginForm.value.username) {\r\n\t\t\tcontext?.$toolUtil.message('请输入用户名', 'error')\r\n\t\t\t\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tif (!loginForm.value.password) {\r\n\t\t\tcontext?.$toolUtil.message('请输入密码', 'error')\r\n\t\t\t\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tif (userList.value.length > 1) {\r\n\t\t\tif (!loginForm.value.role) {\r\n\t\t\t\tcontext?.$toolUtil.message('请选择角色', 'error')\r\n\t\t\t\tverifySlider.value.reset()\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tfor (let i = 0; i < menus.value.length; i++) {\r\n\t\t\t\tif (menus.value[i].roleName == loginForm.value.role) {\r\n\t\t\t\t\ttableName.value = menus.value[i].tableName;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\ttableName.value = userList.value[0].tableName;\r\n\t\t\tloginForm.value.role = userList.value[0].roleName;\r\n\t\t}\r\n\t\tlogin()\r\n\t}\r\n\tconst login = () => {\r\n\t\tcontext?.$http({\r\n\t\t\turl: `${tableName.value}/login?username=${loginForm.value.username}&password=${loginForm.value.password}`,\r\n\t\t\tmethod: 'post'\r\n\t\t}).then(res => {\r\n\t\t\tcontext?.$toolUtil.storageSet(\"Token\", res.data.token);\r\n\t\t\tcontext?.$toolUtil.storageSet(\"role\", loginForm.value.role);\r\n\t\t\tcontext?.$toolUtil.storageSet(\"sessionTable\", tableName.value);\r\n\t\t\tcontext?.$toolUtil.storageSet(\"adminName\", loginForm.value.username);\r\n\t\t\tcontext?.$router.push('/')\r\n\t\t}, err => {\r\n\t\t})\r\n\t}\r\n\t//获取菜单\r\n\tconst getMenu=()=> {\n      let params = {\n        page: 1,\n        limit: 1,\n        sort: 'id',\n      }\n\n      context?.$http({\n        url: \"menu/list\",\n        method: \"get\",\n        params: params\n      }).then(res => {\n          menus.value = JSON.parse(res.data.data.list[0].menujson)\n          for (let i = 0; i < menus.value.length; i++) {\n            if (menus.value[i].hasBackLogin=='是') {\n              userList.value.push(menus.value[i])\n            }\n          }\r\n\t\t\tloginForm.value.role = userList.value[0].roleName\r\n          context?.$toolUtil.storageSet(\"menus\", JSON.stringify(menus.value));\n      })\n    }\r\n\t//初始化\r\n\tconst init = () => {\r\n\t\tgetMenu();\r\n\t}\r\n\tonMounted(()=>{\r\n\t\tinit()\r\n\t\t\r\n\t})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.login_view {\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: cover !important;\r\n\t\tbackground: url(http://clfile.zggen.cn/20240117/c64f30e5ca20441aad10d676999538dc.png);\r\n\t\tdisplay: flex;\r\n\t\tmin-height: 100vh;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tposition: relative;\r\n\t\tbackground-position: center center;\r\n\t\t// 标题盒子\r\n\t\t.outTitle_view {\r\n\t\t\tmargin: -25px 0 0;\r\n\t\t\ttop: 50%;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 29%;\r\n\t\t\tline-height: 50px;\r\n\t\t\tposition: fixed;\r\n\t\t\ttext-align: center;\r\n\t\t\theight: 50px;\r\n\t\t\t.outTilte {\r\n\t\t\t\tcolor: #5EB6FE;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tletter-spacing: 6px;\r\n\t\t\t\tfont-size: 48px;\r\n\t\t\t}\r\n\t\t}\r\n\t\t// 表单盒子\r\n\t\t.login_form {\r\n\t\t\tborder-radius: 10px;\r\n\t\t\tmargin: 0 0 0 184px;\r\n\t\t\tbackground: transparent;\r\n\t\t\tdisplay: flex;\r\n\t\t\twidth: 600px;\r\n\t\t\tjustify-content: center;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t}\r\n\t\t// item盒子\r\n\t\t.list_item {\r\n\t\t\tmargin: 10px 0;\r\n\t\t\tdisplay: flex;\r\n\t\t\twidth: 90%;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\t// label\r\n\t\t\t.list_label {\r\n\t\t\t\twidth: 90px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t\t// 输入框\r\n\t\t\t.list_inp {\r\n\t\t\t\tborder: 0px solid #ddd;\r\n\t\t\t\tborder-radius: 20px;\r\n\t\t\t\tpadding: 0 10px;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\twidth: calc(100% - 90px);\r\n\t\t\t\toutline-offset: 4px;\r\n\t\t\t\tline-height: 36px;\r\n\t\t\t\theight: 36px;\r\n\t\t\t}\r\n\t\t}\r\n\t\t// 用户类型样式\r\n\t\t.tabView{\r\n\t\t\tborder-radius: 10px;\r\n\t\t\tmargin: 0 0 60px;\r\n\t\t\toverflow: hidden;\r\n\t\t\tclip-path: polygon(00% 0%, 100% 00%,  100% 100%,5% 100%, 0 20%);\r\n\t\t\tbackground: #5DB6FF;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tdisplay: flex;\r\n\t\t\twidth: 100%;\r\n\t\t\tfont-size: 18px;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\theight: auto;\r\n\t\t\t// 默认样式\r\n\t\t\t.tab{\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tclip-path: polygon(00% 0%, 100% 00%,  100% 100%,10% 100%, 0 20%);\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tbackground: #5DB6FF;\r\n\t\t\t\tline-height: 40px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\t\t// 选中样式\r\n\t\t\t.tabActive{\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tbox-shadow: 0 4px 6px rgba(0, 0, 0, .3);\r\n\t\t\t\tclip-path: polygon(00% 0%, 80% 00%,95% 90%,  100% 100%,10% 100%, 0 20%);\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tline-height: 40px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\t}\r\n\t\t// 按钮盒子\r\n\t\t.btn_view {\r\n\t\t\tpadding: 20px 0 0;\r\n\t\t\tdisplay: flex;\r\n\t\t\twidth: 100%;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\t// 登录\r\n\t\t\t.login {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 10px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 0 10px 10px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: linear-gradient(270deg, #FB843D 0%, #9EC8EA 0%, #5DB6FF 100%);\r\n\t\t\t\twidth: 80%;\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n</style>", "import script from \"./login.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./login.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./login.vue?vue&type=style&index=0&id=53ca37a8&lang=scss&scoped=true\"\n\nimport exportComponent from \"/yykj/project/back/8082/generator/node_modules_admin/1/node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-53ca37a8\"]])\n\nexport default __exports__"], "names": ["userList", "ref", "menus", "loginForm", "role", "username", "password", "tableName", "loginType", "context", "_getCurrentInstance", "getCurrentInstance", "appContext", "config", "globalProperties", "tabClick", "value", "handleLogin", "length", "$toolUtil", "message", "verifySlider", "reset", "i", "<PERSON><PERSON><PERSON>", "login", "$http", "url", "concat", "method", "then", "res", "storageSet", "data", "token", "$router", "push", "err", "getMenu", "params", "page", "limit", "sort", "JSON", "parse", "list", "<PERSON><PERSON><PERSON>", "hasBackLogin", "stringify", "init", "onMounted", "__exports__"], "sourceRoot": ""}