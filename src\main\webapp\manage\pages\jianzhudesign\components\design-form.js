// 建筑设计表单组件
Vue.component('design-form', {
    props: {
        formData: {
            type: Object,
            default: () => ({})
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    
    data() {
        return {
            form: {
                designcode: '',
                designname: '',
                xiang<PERSON>bianhao: '',
                buildingtype: '',
                architecturalstyle: '',
                buildingarea: null,
                buildingheight: null,
                floors: null,
                basementfloors: 0,
                structuretype: '',
                mainmaterials: '',
                wallmaterials: '',
                roofmaterials: '',
                windowmaterials: '',
                designdrawings: '',
                model3dfile: '',
                cadfile: '',
                renderingimages: '',
                designdescription: '',
                designer: '',
                designdate: new Date(),
                auditstatus: '待审核',
                auditcomments: '',
                budgetamount: null,
                designversion: '1.0',
                isenabled: '是'
            },
            
            rules: {
                designcode: [
                    { required: true, message: '请输入设计编号', trigger: 'blur' }
                ],
                designname: [
                    { required: true, message: '请输入设计名称', trigger: 'blur' }
                ],
                buildingtype: [
                    { required: true, message: '请选择建筑类型', trigger: 'change' }
                ],
                buildingarea: [
                    { required: true, message: '请输入建筑面积', trigger: 'blur' },
                    { type: 'number', min: 0, message: '建筑面积必须大于0', trigger: 'blur' }
                ],
                floors: [
                    { required: true, message: '请输入建筑层数', trigger: 'blur' },
                    { type: 'number', min: 1, message: '建筑层数必须大于0', trigger: 'blur' }
                ],
                designer: [
                    { required: true, message: '请输入设计师', trigger: 'blur' }
                ]
            },
            
            // 选项数据
            buildingTypes: [
                '住宅建筑', '商业建筑', '办公建筑', '工业建筑', '公共建筑', '教育建筑', '医疗建筑', '文化建筑'
            ],
            
            architecturalStyles: [
                '现代简约', '欧式古典', '中式传统', '地中海', '美式乡村', '北欧风格', '工业风格', '新中式'
            ],
            
            structureTypes: [
                '框架结构', '剪力墙结构', '框剪结构', '钢结构', '砖混结构', '木结构', '装配式结构'
            ],
            
            materialOptions: [
                '钢筋混凝土', '钢材', '木材', '砖石', '玻璃', '铝合金', '复合材料', '环保材料'
            ],
            
            loading: false
        }
    },
    
    watch: {
        formData: {
            handler(newVal) {
                if (newVal && Object.keys(newVal).length > 0) {
                    this.form = { ...this.form, ...newVal };
                }
            },
            immediate: true,
            deep: true
        }
    },
    
    methods: {
        // 提交表单
        submitForm() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.loading = true;
                    
                    // 处理表单数据
                    const submitData = { ...this.form };
                    
                    // 格式化日期
                    if (submitData.designdate) {
                        submitData.designdate = this.formatDate(submitData.designdate);
                    }
                    
                    this.$emit('submit', submitData);
                    this.loading = false;
                } else {
                    this.$message.error('请检查表单填写是否正确');
                }
            });
        },
        
        // 取消
        cancel() {
            this.$emit('cancel');
        },
        
        // 重置表单
        resetForm() {
            this.$refs.form.resetFields();
        },
        
        // 文件上传处理
        handleFileUpload(file, type) {
            // 这里应该实现文件上传逻辑
            console.log('上传文件:', file, '类型:', type);
            
            // 模拟上传成功
            const fileName = file.name;
            const fileUrl = `/uploads/${type}/${fileName}`;
            
            switch (type) {
                case 'drawings':
                    this.form.designdrawings = fileUrl;
                    break;
                case 'model3d':
                    this.form.model3dfile = fileUrl;
                    break;
                case 'cad':
                    this.form.cadfile = fileUrl;
                    break;
                case 'rendering':
                    this.form.renderingimages = fileUrl;
                    break;
            }
            
            this.$message.success('文件上传成功');
            return false; // 阻止默认上传行为
        },
        
        // 预览文件
        previewFile(url, type) {
            if (!url) {
                this.$message.warning('没有文件可预览');
                return;
            }
            
            // 根据文件类型打开预览
            if (type === 'image') {
                // 图片预览
                this.$alert(`<img src="${url}" style="max-width: 100%; max-height: 400px;">`, '图片预览', {
                    dangerouslyUseHTMLString: true
                });
            } else {
                // 其他文件类型打开新窗口
                window.open(url, '_blank');
            }
        },
        
        // 工具方法
        formatDate(date) {
            if (!date) return '';
            const d = new Date(date);
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
    },
    
    template: `
        <div class="design-form">
            <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="medium">
                <el-tabs type="border-card">
                    <!-- 基本信息 -->
                    <el-tab-pane label="基本信息" name="basic">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="设计编号" prop="designcode">
                                    <el-input v-model="form.designcode" :disabled="isEdit"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="设计名称" prop="designname">
                                    <el-input v-model="form.designname"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="项目编号">
                                    <el-input v-model="form.xiangmubianhao"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="建筑类型" prop="buildingtype">
                                    <el-select v-model="form.buildingtype" placeholder="请选择建筑类型">
                                        <el-option v-for="type in buildingTypes" :key="type" :label="type" :value="type"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="建筑风格">
                                    <el-select v-model="form.architecturalstyle" placeholder="请选择建筑风格">
                                        <el-option v-for="style in architecturalStyles" :key="style" :label="style" :value="style"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="结构类型">
                                    <el-select v-model="form.structuretype" placeholder="请选择结构类型">
                                        <el-option v-for="type in structureTypes" :key="type" :label="type" :value="type"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="设计师" prop="designer">
                                    <el-input v-model="form.designer"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="设计日期">
                                    <el-date-picker v-model="form.designdate" type="date" placeholder="选择日期"></el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                    
                    <!-- 建筑参数 -->
                    <el-tab-pane label="建筑参数" name="parameters">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item label="建筑面积" prop="buildingarea">
                                    <el-input-number v-model="form.buildingarea" :min="0" :precision="2" controls-position="right">
                                        <template slot="append">㎡</template>
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="建筑高度">
                                    <el-input-number v-model="form.buildingheight" :min="0" :precision="2" controls-position="right">
                                        <template slot="append">m</template>
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="建筑层数" prop="floors">
                                    <el-input-number v-model="form.floors" :min="1" controls-position="right">
                                        <template slot="append">层</template>
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item label="地下层数">
                                    <el-input-number v-model="form.basementfloors" :min="0" controls-position="right">
                                        <template slot="append">层</template>
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="预算金额">
                                    <el-input-number v-model="form.budgetamount" :min="0" :precision="2" controls-position="right">
                                        <template slot="append">元</template>
                                    </el-input-number>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="设计版本">
                                    <el-input v-model="form.designversion"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                    
                    <!-- 材料信息 -->
                    <el-tab-pane label="材料信息" name="materials">
                        <el-form-item label="主要材料">
                            <el-select v-model="form.mainmaterials" multiple placeholder="请选择主要材料">
                                <el-option v-for="material in materialOptions" :key="material" :label="material" :value="material"></el-option>
                            </el-select>
                        </el-form-item>
                        
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item label="外墙材料">
                                    <el-input v-model="form.wallmaterials"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="屋顶材料">
                                    <el-input v-model="form.roofmaterials"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="门窗材料">
                                    <el-input v-model="form.windowmaterials"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                    
                    <!-- 文件资料 -->
                    <el-tab-pane label="文件资料" name="files">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="设计图纸">
                                    <div class="file-upload-area">
                                        <el-upload
                                            :before-upload="(file) => handleFileUpload(file, 'drawings')"
                                            :show-file-list="false"
                                            accept=".pdf,.dwg,.jpg,.png">
                                            <el-button size="small" type="primary">上传图纸</el-button>
                                        </el-upload>
                                        <div v-if="form.designdrawings" class="file-info">
                                            <span>{{form.designdrawings}}</span>
                                            <el-button size="mini" type="text" @click="previewFile(form.designdrawings, 'file')">预览</el-button>
                                        </div>
                                    </div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="3D模型文件">
                                    <div class="file-upload-area">
                                        <el-upload
                                            :before-upload="(file) => handleFileUpload(file, 'model3d')"
                                            :show-file-list="false"
                                            accept=".3ds,.obj,.fbx,.blend">
                                            <el-button size="small" type="primary">上传模型</el-button>
                                        </el-upload>
                                        <div v-if="form.model3dfile" class="file-info">
                                            <span>{{form.model3dfile}}</span>
                                            <el-button size="mini" type="text" @click="previewFile(form.model3dfile, 'file')">预览</el-button>
                                        </div>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="CAD文件">
                                    <div class="file-upload-area">
                                        <el-upload
                                            :before-upload="(file) => handleFileUpload(file, 'cad')"
                                            :show-file-list="false"
                                            accept=".dwg,.dxf">
                                            <el-button size="small" type="primary">上传CAD</el-button>
                                        </el-upload>
                                        <div v-if="form.cadfile" class="file-info">
                                            <span>{{form.cadfile}}</span>
                                            <el-button size="mini" type="text" @click="previewFile(form.cadfile, 'file')">预览</el-button>
                                        </div>
                                    </div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="效果图">
                                    <div class="file-upload-area">
                                        <el-upload
                                            :before-upload="(file) => handleFileUpload(file, 'rendering')"
                                            :show-file-list="false"
                                            accept=".jpg,.png,.jpeg">
                                            <el-button size="small" type="primary">上传效果图</el-button>
                                        </el-upload>
                                        <div v-if="form.renderingimages" class="file-info">
                                            <img :src="form.renderingimages" style="width: 100px; height: 60px; object-fit: cover;">
                                            <el-button size="mini" type="text" @click="previewFile(form.renderingimages, 'image')">预览</el-button>
                                        </div>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-form-item label="设计说明">
                            <el-input
                                v-model="form.designdescription"
                                type="textarea"
                                :rows="4"
                                placeholder="请输入设计说明...">
                            </el-input>
                        </el-form-item>
                    </el-tab-pane>
                </el-tabs>
                
                <div class="form-actions">
                    <el-button @click="cancel">取消</el-button>
                    <el-button type="primary" @click="submitForm" :loading="loading">
                        {{isEdit ? '更新' : '创建'}}
                    </el-button>
                </div>
            </el-form>
        </div>
    `,
    
    style: `
        <style scoped>
        .design-form {
            padding: 20px;
        }
        
        .file-upload-area {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            padding: 20px;
            text-align: center;
            background: #fafafa;
        }
        
        .file-info {
            margin-top: 10px;
            padding: 10px;
            background: #f0f9ff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .form-actions {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ebeef5;
        }
        
        .form-actions .el-button {
            margin: 0 10px;
            min-width: 100px;
        }
        </style>
    `
});
