// 仪表板主页面逻辑
new Vue({
    el: '#app',
    data() {
        return {
            // 用户信息
            userName: '管理员',
            currentDate: '',
            
            // 核心指标
            totalProjects: 156,
            projectsChange: 12.5,
            totalDesigns: 89,
            designsChange: 8.3,
            totalBudget: 25680000,
            budgetChange: 15.2,
            avgProgress: 68,
            progressChange: 5.7,
            
            // 项目状态
            ongoingProjects: 45,
            completedProjects: 98,
            pausedProjects: 13,
            
            // 图表数据
            progressPeriod: 'month',
            
            // 待办事项
            todoList: [
                { id: 1, title: '审核新设计方案', completed: false, priority: 'high' },
                { id: 2, title: '更新项目进度报告', completed: false, priority: 'medium' },
                { id: 3, title: '检查材料库存', completed: true, priority: 'low' },
                { id: 4, title: '安排下周工作会议', completed: false, priority: 'medium' },
                { id: 5, title: '回复客户邮件', completed: false, priority: 'high' }
            ],
            
            // 最近活动
            recentActivities: [
                {
                    id: 1,
                    type: 'project',
                    icon: 'fas fa-building',
                    title: '新项目创建',
                    description: '创建了"绿城花园住宅小区"项目',
                    time: '2小时前'
                },
                {
                    id: 2,
                    type: 'design',
                    icon: 'fas fa-drafting-compass',
                    title: '设计方案提交',
                    description: '张建筑师提交了商业综合体设计方案',
                    time: '4小时前'
                },
                {
                    id: 3,
                    type: 'material',
                    icon: 'fas fa-cubes',
                    title: '材料入库',
                    description: '新增钢筋材料500吨',
                    time: '6小时前'
                },
                {
                    id: 4,
                    type: 'system',
                    icon: 'fas fa-check-circle',
                    title: '项目审核通过',
                    description: '"阳光城市广场"项目通过初步审核',
                    time: '1天前'
                }
            ],
            
            // 通知
            notifications: [
                {
                    id: 1,
                    type: 'warning',
                    icon: 'fas fa-exclamation-triangle',
                    title: '库存预警',
                    time: '30分钟前',
                    read: false
                },
                {
                    id: 2,
                    type: 'info',
                    icon: 'fas fa-info-circle',
                    title: '系统更新',
                    time: '2小时前',
                    read: false
                },
                {
                    id: 3,
                    type: 'success',
                    icon: 'fas fa-check-circle',
                    title: '项目完成',
                    time: '1天前',
                    read: true
                }
            ],
            
            // 图表实例
            charts: {}
        }
    },
    
    computed: {
        unreadNotifications() {
            return this.notifications.filter(n => !n.read).length;
        }
    },
    
    mounted() {
        this.initCurrentDate();
        this.initCharts();
        this.loadDashboardData();
    },
    
    methods: {
        // 初始化当前日期
        initCurrentDate() {
            const now = new Date();
            const options = { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                weekday: 'long'
            };
            this.currentDate = now.toLocaleDateString('zh-CN', options);
        },
        
        // 初始化图表
        initCharts() {
            this.$nextTick(() => {
                this.initMiniCharts();
                this.initProgressOverviewChart();
                this.initBuildingTypeChart();
            });
        },
        
        // 初始化迷你图表
        initMiniCharts() {
            // 项目数量趋势
            const projectsChart = echarts.init(document.getElementById('projectsChart'));
            projectsChart.setOption({
                grid: { top: 0, right: 0, bottom: 0, left: 0 },
                xAxis: { type: 'category', show: false, data: ['1', '2', '3', '4', '5', '6', '7'] },
                yAxis: { type: 'value', show: false },
                series: [{
                    type: 'line',
                    data: [120, 132, 101, 134, 90, 230, 210],
                    smooth: true,
                    symbol: 'none',
                    lineStyle: { color: '#667eea', width: 2 },
                    areaStyle: { color: 'rgba(102, 126, 234, 0.1)' }
                }]
            });
            this.charts.projects = projectsChart;
            
            // 设计方案趋势
            const designsChart = echarts.init(document.getElementById('designsChart'));
            designsChart.setOption({
                grid: { top: 0, right: 0, bottom: 0, left: 0 },
                xAxis: { type: 'category', show: false, data: ['1', '2', '3', '4', '5', '6', '7'] },
                yAxis: { type: 'value', show: false },
                series: [{
                    type: 'line',
                    data: [80, 85, 78, 92, 88, 95, 89],
                    smooth: true,
                    symbol: 'none',
                    lineStyle: { color: '#f093fb', width: 2 },
                    areaStyle: { color: 'rgba(240, 147, 251, 0.1)' }
                }]
            });
            this.charts.designs = designsChart;
            
            // 预算趋势
            const budgetChart = echarts.init(document.getElementById('budgetChart'));
            budgetChart.setOption({
                grid: { top: 0, right: 0, bottom: 0, left: 0 },
                xAxis: { type: 'category', show: false, data: ['1', '2', '3', '4', '5', '6', '7'] },
                yAxis: { type: 'value', show: false },
                series: [{
                    type: 'bar',
                    data: [2000, 2200, 1800, 2400, 2100, 2600, 2300],
                    itemStyle: { color: '#43e97b' }
                }]
            });
            this.charts.budget = budgetChart;
            
            // 进度趋势
            const progressChart = echarts.init(document.getElementById('progressChart'));
            progressChart.setOption({
                grid: { top: 0, right: 0, bottom: 0, left: 0 },
                xAxis: { type: 'category', show: false, data: ['1', '2', '3', '4', '5', '6', '7'] },
                yAxis: { type: 'value', show: false },
                series: [{
                    type: 'line',
                    data: [60, 62, 65, 63, 66, 68, 68],
                    smooth: true,
                    symbol: 'none',
                    lineStyle: { color: '#4facfe', width: 2 },
                    areaStyle: { color: 'rgba(79, 172, 254, 0.1)' }
                }]
            });
            this.charts.progress = progressChart;
        },
        
        // 初始化项目进度概览图表
        initProgressOverviewChart() {
            const chart = echarts.init(document.getElementById('progressOverviewChart'));
            chart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'cross' }
                },
                legend: {
                    data: ['计划进度', '实际进度', '预算使用']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '进度(%)',
                        position: 'left'
                    },
                    {
                        type: 'value',
                        name: '预算(万元)',
                        position: 'right'
                    }
                ],
                series: [
                    {
                        name: '计划进度',
                        type: 'line',
                        data: [10, 20, 35, 50, 65, 80, 95],
                        smooth: true,
                        lineStyle: { color: '#667eea' }
                    },
                    {
                        name: '实际进度',
                        type: 'line',
                        data: [8, 18, 32, 48, 62, 75, 88],
                        smooth: true,
                        lineStyle: { color: '#f093fb' }
                    },
                    {
                        name: '预算使用',
                        type: 'bar',
                        yAxisIndex: 1,
                        data: [500, 800, 1200, 1800, 2200, 2500, 2800],
                        itemStyle: { color: '#43e97b' }
                    }
                ]
            });
            this.charts.progressOverview = chart;
        },
        
        // 初始化建筑类型分布图表
        initBuildingTypeChart() {
            const chart = echarts.init(document.getElementById('buildingTypeChart'));
            chart.setOption({
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left'
                },
                series: [
                    {
                        name: '建筑类型',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        center: ['60%', '50%'],
                        data: [
                            { value: 35, name: '住宅建筑' },
                            { value: 25, name: '商业建筑' },
                            { value: 20, name: '办公建筑' },
                            { value: 12, name: '工业建筑' },
                            { value: 8, name: '公共建筑' }
                        ],
                        itemStyle: {
                            borderRadius: 5,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            });
            this.charts.buildingType = chart;
        },
        
        // 加载仪表板数据
        async loadDashboardData() {
            try {
                // 这里应该从后端API获取实际数据
                // const response = await axios.get('/api/dashboard/data');
                // 模拟数据加载
                console.log('加载仪表板数据...');
            } catch (error) {
                console.error('加载仪表板数据失败:', error);
            }
        },
        
        // 加载进度数据
        loadProgressData() {
            // 根据选择的时间周期重新加载数据
            console.log('加载进度数据:', this.progressPeriod);
        },
        
        // 快速操作
        createProject() {
            this.$message.info('跳转到新建项目页面...');
            // window.location.href = '/manage/pages/xiangmuxinxi/index.html';
        },
        
        createDesign() {
            this.$message.info('跳转到新建设计页面...');
            // window.location.href = '/manage/pages/jianzhudesign/index.html';
        },
        
        addMaterial() {
            this.$message.info('跳转到添加材料页面...');
            // window.location.href = '/manage/pages/buildingmaterial/index.html';
        },
        
        generateReport() {
            this.$message.info('生成报告功能开发中...');
        },
        
        // 待办事项操作
        updateTodo(todo) {
            console.log('更新待办事项:', todo);
        },
        
        addTodo() {
            this.$prompt('请输入待办事项', '添加待办', {
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            }).then(({ value }) => {
                if (value) {
                    const newTodo = {
                        id: Date.now(),
                        title: value,
                        completed: false,
                        priority: 'medium'
                    };
                    this.todoList.unshift(newTodo);
                    this.$message.success('添加成功');
                }
            });
        },
        
        // 查看所有活动
        viewAllActivities() {
            this.$message.info('跳转到活动日志页面...');
        },
        
        // 查看所有通知
        viewAllNotifications() {
            this.$message.info('跳转到通知中心页面...');
        },
        
        // 格式化金额
        formatMoney(amount) {
            if (!amount) return '0';
            return (amount / 10000).toFixed(1) + '万';
        }
    },
    
    // 窗口大小改变时重新调整图表
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
    },
    
    created() {
        window.addEventListener('resize', this.handleResize);
    },
    
    methods: {
        ...this.methods,
        handleResize() {
            Object.values(this.charts).forEach(chart => {
                if (chart && chart.resize) {
                    chart.resize();
                }
            });
        }
    }
});
