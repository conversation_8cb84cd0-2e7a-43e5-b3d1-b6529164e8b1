package com.cl.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Map;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

import com.cl.utils.ValidatorUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.cl.entity.BuildingmaterialEntity;
import com.cl.entity.view.BuildingmaterialView;
import com.cl.service.BuildingmaterialService;
import com.cl.service.TokenService;
import com.cl.utils.PageUtils;
import com.cl.utils.R;
import com.cl.utils.MPUtil;
import com.cl.utils.CommonUtil;
import java.io.IOException;

/**
 * 建筑材料库
 * 后端接口
 * <AUTHOR> @email 
 * @date 2024-01-25 13:22:32
 */
@RestController
@RequestMapping("/buildingmaterial")
public class BuildingmaterialController {
    @Autowired
    private BuildingmaterialService buildingmaterialService;

    @Autowired
    private TokenService tokenService;

    /**
     * 列表
     */
    @RequestMapping("/page")
    public R page(@RequestParam Map<String, Object> params, BuildingmaterialEntity buildingmaterial,
                  HttpServletRequest request) {
        EntityWrapper<BuildingmaterialEntity> ew = new EntityWrapper<BuildingmaterialEntity>();

        PageUtils page = buildingmaterialService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.likeOrEq(ew, buildingmaterial), params), params));

        return R.ok().put("data", page);
    }

    /**
     * 列表
     */
    @RequestMapping("/list")
    public R list(BuildingmaterialEntity buildingmaterial) {
        EntityWrapper<BuildingmaterialEntity> ew = new EntityWrapper<BuildingmaterialEntity>();
        ew.allEq(MPUtil.allEQMapPre(buildingmaterial, "buildingmaterial"));
        return R.ok().put("data", buildingmaterialService.selectListView(ew));
    }

    /**
     * 查询
     */
    @RequestMapping("/query")
    public R query(BuildingmaterialEntity buildingmaterial) {
        EntityWrapper<BuildingmaterialEntity> ew = new EntityWrapper<BuildingmaterialEntity>();
        ew.allEq(MPUtil.allEQMapPre(buildingmaterial, "buildingmaterial"));
        BuildingmaterialView buildingmaterialView = buildingmaterialService.selectView(ew);
        return R.ok("查询建筑材料成功").put("data", buildingmaterialView);
    }

    /**
     * 后端详情
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id) {
        BuildingmaterialEntity buildingmaterial = buildingmaterialService.selectById(id);
        return R.ok().put("data", buildingmaterial);
    }

    /**
     * 前端详情
     */
    @RequestMapping("/detail/{id}")
    public R detail(@PathVariable("id") Long id) {
        BuildingmaterialEntity buildingmaterial = buildingmaterialService.selectById(id);
        return R.ok().put("data", buildingmaterial);
    }

    /**
     * 后端保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody BuildingmaterialEntity buildingmaterial, HttpServletRequest request) {
        buildingmaterial.setId(new Date().getTime() + new Double(Math.floor(Math.random() * 1000)).longValue());
        //ValidatorUtils.validateEntity(buildingmaterial);
        buildingmaterialService.insert(buildingmaterial);
        return R.ok();
    }

    /**
     * 前端保存
     */
    @RequestMapping("/add")
    public R add(@RequestBody BuildingmaterialEntity buildingmaterial, HttpServletRequest request) {
        buildingmaterial.setId(new Date().getTime() + new Double(Math.floor(Math.random() * 1000)).longValue());
        //ValidatorUtils.validateEntity(buildingmaterial);
        buildingmaterialService.insert(buildingmaterial);
        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @Transactional
    public R update(@RequestBody BuildingmaterialEntity buildingmaterial, HttpServletRequest request) {
        //ValidatorUtils.validateEntity(buildingmaterial);
        buildingmaterialService.updateById(buildingmaterial);//全部更新
        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids) {
        buildingmaterialService.deleteBatchIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 库存预警
     */
    @RequestMapping("/stockAlert")
    public R stockAlert() {
        List<BuildingmaterialEntity> alertList = buildingmaterialService.getStockAlertList();
        return R.ok().put("data", alertList);
    }

    /**
     * 材料类别统计
     */
    @RequestMapping("/categoryStats")
    public R categoryStats() {
        List<Map<String, Object>> stats = buildingmaterialService.getCategoryStats();
        return R.ok().put("data", stats);
    }

    /**
     * 供应商统计
     */
    @RequestMapping("/supplierStats")
    public R supplierStats() {
        List<Map<String, Object>> stats = buildingmaterialService.getSupplierStats();
        return R.ok().put("data", stats);
    }

    /**
     * 价格趋势分析
     */
    @RequestMapping("/priceAnalysis")
    public R priceAnalysis(@RequestParam String materialcode) {
        List<Map<String, Object>> analysis = buildingmaterialService.getPriceAnalysis(materialcode);
        return R.ok().put("data", analysis);
    }

    /**
     * 批量导入材料
     */
    @RequestMapping("/batchImport")
    public R batchImport(@RequestBody List<BuildingmaterialEntity> materials, HttpServletRequest request) {
        for (BuildingmaterialEntity material : materials) {
            material.setId(new Date().getTime() + new Double(Math.floor(Math.random() * 1000)).longValue());
            material.setAddtime(new Date());
        }
        buildingmaterialService.insertBatch(materials);
        return R.ok("批量导入成功");
    }

    /**
     * 材料推荐
     */
    @RequestMapping("/recommend")
    public R recommend(@RequestParam String buildingtype, @RequestParam String category) {
        List<BuildingmaterialEntity> recommendations = buildingmaterialService.getRecommendations(buildingtype, category);
        return R.ok().put("data", recommendations);
    }
}
