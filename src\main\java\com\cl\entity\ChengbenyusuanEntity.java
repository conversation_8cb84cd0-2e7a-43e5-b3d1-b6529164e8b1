package com.cl.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.lang.reflect.InvocationTargetException;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.beanutils.BeanUtils;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.enums.FieldFill;
import com.baomidou.mybatisplus.enums.IdType;


/**
 * 成本预算
 * 数据库通用操作实体类（普通增删改查）
 * <AUTHOR> @email 
 * @date 2024-01-25 13:22:32
 */
@TableName("chengbenyusuan")
public class ChengbenyusuanEntity<T> implements Serializable {
	private static final long serialVersionUID = 1L;


	public ChengbenyusuanEntity() {
		
	}
	
	public ChengbenyusuanEntity(T t) {
		try {
			BeanUtils.copyProperties(this, t);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	/**
	 * 主键id
	 */
	@TableId
	private Long id;
	/**
	 * 项目编号
	 */
					
	private String xiangmubianhao;
	
	/**
	 * 项目名称
	 */
					
	private String xiangmumingcheng;
	
	/**
	 * 经理账号
	 */
					
	private String jinglizhanghao;
	
	/**
	 * 经理姓名
	 */
					
	private String jinglixingming;
	
	/**
	 * 预算编制
	 */
					
	private String yusuanbianzhi;
	
	/**
	 * 成本核算
	 */
					
	private String chengbenhesuan;
	
	/**
	 * 成本控制
	 */
					
	private String chengbenkongzhi;
	
	
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat
	private Date addtime;

	public Date getAddtime() {
		return addtime;
	}
	public void setAddtime(Date addtime) {
		this.addtime = addtime;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	/**
	 * 设置：项目编号
	 */
	public void setXiangmubianhao(String xiangmubianhao) {
		this.xiangmubianhao = xiangmubianhao;
	}
	/**
	 * 获取：项目编号
	 */
	public String getXiangmubianhao() {
		return xiangmubianhao;
	}
	/**
	 * 设置：项目名称
	 */
	public void setXiangmumingcheng(String xiangmumingcheng) {
		this.xiangmumingcheng = xiangmumingcheng;
	}
	/**
	 * 获取：项目名称
	 */
	public String getXiangmumingcheng() {
		return xiangmumingcheng;
	}
	/**
	 * 设置：经理账号
	 */
	public void setJinglizhanghao(String jinglizhanghao) {
		this.jinglizhanghao = jinglizhanghao;
	}
	/**
	 * 获取：经理账号
	 */
	public String getJinglizhanghao() {
		return jinglizhanghao;
	}
	/**
	 * 设置：经理姓名
	 */
	public void setJinglixingming(String jinglixingming) {
		this.jinglixingming = jinglixingming;
	}
	/**
	 * 获取：经理姓名
	 */
	public String getJinglixingming() {
		return jinglixingming;
	}
	/**
	 * 设置：预算编制
	 */
	public void setYusuanbianzhi(String yusuanbianzhi) {
		this.yusuanbianzhi = yusuanbianzhi;
	}
	/**
	 * 获取：预算编制
	 */
	public String getYusuanbianzhi() {
		return yusuanbianzhi;
	}
	/**
	 * 设置：成本核算
	 */
	public void setChengbenhesuan(String chengbenhesuan) {
		this.chengbenhesuan = chengbenhesuan;
	}
	/**
	 * 获取：成本核算
	 */
	public String getChengbenhesuan() {
		return chengbenhesuan;
	}
	/**
	 * 设置：成本控制
	 */
	public void setChengbenkongzhi(String chengbenkongzhi) {
		this.chengbenkongzhi = chengbenkongzhi;
	}
	/**
	 * 获取：成本控制
	 */
	public String getChengbenkongzhi() {
		return chengbenkongzhi;
	}

}
