<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目仪表板 - 建筑工程项目管理系统</title>
    <link rel="stylesheet" href="../../css/element-ui.css">
    <link rel="stylesheet" href="../../css/common.css">
    <link rel="stylesheet" href="./dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
</head>
<body>
    <div id="app">
        <div class="dashboard-container">
            <!-- 欢迎横幅 -->
            <div class="welcome-banner">
                <div class="banner-content">
                    <div class="welcome-text">
                        <h1>欢迎回来，{{userName}}！</h1>
                        <p>今天是 {{currentDate}}，让我们开始新的一天工作</p>
                    </div>
                    <div class="weather-info">
                        <div class="weather-icon">
                            <i class="fas fa-sun"></i>
                        </div>
                        <div class="weather-details">
                            <span class="temperature">25°C</span>
                            <span class="weather-desc">晴朗</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 核心指标卡片 -->
            <div class="metrics-grid">
                <div class="metric-card projects">
                    <div class="metric-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="metric-content">
                        <h3>{{totalProjects}}</h3>
                        <p>总项目数</p>
                        <span class="metric-change positive">+{{projectsChange}}%</span>
                    </div>
                    <div class="metric-chart">
                        <div class="mini-chart" id="projectsChart"></div>
                    </div>
                </div>

                <div class="metric-card designs">
                    <div class="metric-icon">
                        <i class="fas fa-drafting-compass"></i>
                    </div>
                    <div class="metric-content">
                        <h3>{{totalDesigns}}</h3>
                        <p>设计方案</p>
                        <span class="metric-change positive">+{{designsChange}}%</span>
                    </div>
                    <div class="metric-chart">
                        <div class="mini-chart" id="designsChart"></div>
                    </div>
                </div>

                <div class="metric-card budget">
                    <div class="metric-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="metric-content">
                        <h3>¥{{formatMoney(totalBudget)}}</h3>
                        <p>总预算</p>
                        <span class="metric-change positive">+{{budgetChange}}%</span>
                    </div>
                    <div class="metric-chart">
                        <div class="mini-chart" id="budgetChart"></div>
                    </div>
                </div>

                <div class="metric-card progress">
                    <div class="metric-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="metric-content">
                        <h3>{{avgProgress}}%</h3>
                        <p>平均进度</p>
                        <span class="metric-change positive">+{{progressChange}}%</span>
                    </div>
                    <div class="metric-chart">
                        <div class="mini-chart" id="progressChart"></div>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <el-row :gutter="20">
                    <!-- 左侧内容 -->
                    <el-col :span="16">
                        <!-- 项目进度概览 -->
                        <div class="content-card">
                            <div class="card-header">
                                <h3><i class="fas fa-chart-line"></i> 项目进度概览</h3>
                                <el-select v-model="progressPeriod" size="small" @change="loadProgressData">
                                    <el-option label="本周" value="week"></el-option>
                                    <el-option label="本月" value="month"></el-option>
                                    <el-option label="本季度" value="quarter"></el-option>
                                </el-select>
                            </div>
                            <div class="chart-container">
                                <div id="progressOverviewChart" style="height: 300px;"></div>
                            </div>
                        </div>

                        <!-- 建筑类型分布 -->
                        <div class="content-card">
                            <div class="card-header">
                                <h3><i class="fas fa-chart-pie"></i> 建筑类型分布</h3>
                            </div>
                            <div class="chart-container">
                                <div id="buildingTypeChart" style="height: 300px;"></div>
                            </div>
                        </div>

                        <!-- 最近活动 -->
                        <div class="content-card">
                            <div class="card-header">
                                <h3><i class="fas fa-history"></i> 最近活动</h3>
                                <el-button size="small" type="text" @click="viewAllActivities">查看全部</el-button>
                            </div>
                            <div class="activity-list">
                                <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
                                    <div class="activity-icon" :class="activity.type">
                                        <i :class="activity.icon"></i>
                                    </div>
                                    <div class="activity-content">
                                        <p class="activity-title">{{activity.title}}</p>
                                        <p class="activity-desc">{{activity.description}}</p>
                                        <span class="activity-time">{{activity.time}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-col>

                    <!-- 右侧内容 -->
                    <el-col :span="8">
                        <!-- 快速操作 -->
                        <div class="content-card">
                            <div class="card-header">
                                <h3><i class="fas fa-bolt"></i> 快速操作</h3>
                            </div>
                            <div class="quick-actions">
                                <div class="action-item" @click="createProject">
                                    <div class="action-icon">
                                        <i class="fas fa-plus"></i>
                                    </div>
                                    <span>新建项目</span>
                                </div>
                                <div class="action-item" @click="createDesign">
                                    <div class="action-icon">
                                        <i class="fas fa-drafting-compass"></i>
                                    </div>
                                    <span>新建设计</span>
                                </div>
                                <div class="action-item" @click="addMaterial">
                                    <div class="action-icon">
                                        <i class="fas fa-cubes"></i>
                                    </div>
                                    <span>添加材料</span>
                                </div>
                                <div class="action-item" @click="generateReport">
                                    <div class="action-icon">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <span>生成报告</span>
                                </div>
                            </div>
                        </div>

                        <!-- 待办事项 -->
                        <div class="content-card">
                            <div class="card-header">
                                <h3><i class="fas fa-check-square"></i> 待办事项</h3>
                                <el-button size="small" type="text" @click="addTodo">添加</el-button>
                            </div>
                            <div class="todo-list">
                                <div class="todo-item" v-for="todo in todoList" :key="todo.id">
                                    <el-checkbox v-model="todo.completed" @change="updateTodo(todo)"></el-checkbox>
                                    <span :class="{'completed': todo.completed}">{{todo.title}}</span>
                                    <span class="todo-priority" :class="todo.priority">{{todo.priority}}</span>
                                </div>
                            </div>
                        </div>

                        <!-- 项目状态 -->
                        <div class="content-card">
                            <div class="card-header">
                                <h3><i class="fas fa-project-diagram"></i> 项目状态</h3>
                            </div>
                            <div class="project-status">
                                <div class="status-item">
                                    <div class="status-label">进行中</div>
                                    <div class="status-value">{{ongoingProjects}}</div>
                                    <div class="status-bar">
                                        <div class="status-progress" :style="{width: (ongoingProjects/totalProjects*100) + '%'}"></div>
                                    </div>
                                </div>
                                <div class="status-item">
                                    <div class="status-label">已完成</div>
                                    <div class="status-value">{{completedProjects}}</div>
                                    <div class="status-bar">
                                        <div class="status-progress completed" :style="{width: (completedProjects/totalProjects*100) + '%'}"></div>
                                    </div>
                                </div>
                                <div class="status-item">
                                    <div class="status-label">已暂停</div>
                                    <div class="status-value">{{pausedProjects}}</div>
                                    <div class="status-bar">
                                        <div class="status-progress paused" :style="{width: (pausedProjects/totalProjects*100) + '%'}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 通知中心 -->
                        <div class="content-card">
                            <div class="card-header">
                                <h3><i class="fas fa-bell"></i> 通知中心</h3>
                                <el-badge :value="unreadNotifications" class="notification-badge">
                                    <el-button size="small" type="text" @click="viewAllNotifications">查看全部</el-button>
                                </el-badge>
                            </div>
                            <div class="notification-list">
                                <div class="notification-item" v-for="notification in notifications" :key="notification.id" :class="{'unread': !notification.read}">
                                    <div class="notification-icon" :class="notification.type">
                                        <i :class="notification.icon"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p class="notification-title">{{notification.title}}</p>
                                        <span class="notification-time">{{notification.time}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
    </div>

    <script src="../../js/vue.js"></script>
    <script src="../../js/element-ui.js"></script>
    <script src="../../js/axios.js"></script>
    <script src="./dashboard.js"></script>
</body>
</html>
