package com.cl.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.lang.reflect.InvocationTargetException;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.beanutils.BeanUtils;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.enums.FieldFill;
import com.baomidou.mybatisplus.enums.IdType;

/**
 * 建筑材料库
 * 数据库通用操作实体类（普通增删改查）
 * <AUTHOR> @email 
 * @date 2024-01-25 13:22:32
 */
@TableName("buildingmaterial")
public class BuildingmaterialEntity<T> implements Serializable {
	private static final long serialVersionUID = 1L;

	public BuildingmaterialEntity() {
		
	}
	
	public BuildingmaterialEntity(T t) {
		try {
			BeanUtils.copyProperties(this, t);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	/**
	 * 主键id
	 */
	@TableId
	private Long id;
	
	/**
	 * 材料编号
	 */
	private String materialcode;
	
	/**
	 * 材料名称
	 */
	private String materialname;
	
	/**
	 * 材料类别
	 */
	private String materialcategory;
	
	/**
	 * 材料品牌
	 */
	private String materialbrand;
	
	/**
	 * 材料规格
	 */
	private String materialspec;
	
	/**
	 * 材料单位
	 */
	private String materialunit;
	
	/**
	 * 材料单价
	 */
	private Double materialprice;
	
	/**
	 * 材料图片
	 */
	private String materialimage;
	
	/**
	 * 材料描述
	 */
	private String materialdescription;
	
	/**
	 * 供应商
	 */
	private String supplier;
	
	/**
	 * 供应商联系方式
	 */
	private String suppliercontact;
	
	/**
	 * 材料质量等级
	 */
	private String qualitygrade;
	
	/**
	 * 环保等级
	 */
	private String environmentalgrade;
	
	/**
	 * 防火等级
	 */
	private String firegrade;
	
	/**
	 * 使用寿命
	 */
	private Integer servicelife;
	
	/**
	 * 维护周期
	 */
	private Integer maintenancecycle;
	
	/**
	 * 适用部位
	 */
	private String applicableparts;
	
	/**
	 * 技术参数
	 */
	private String technicalparameters;
	
	/**
	 * 安装要求
	 */
	private String installationrequirements;
	
	/**
	 * 库存数量
	 */
	private Integer stockquantity;
	
	/**
	 * 最小库存
	 */
	private Integer minstock;
	
	/**
	 * 是否启用
	 */
	private String isenabled;
	
	/**
	 * 创建时间
	 */
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat
	private Date addtime;

	public Date getAddtime() {
		return addtime;
	}
	public void setAddtime(Date addtime) {
		this.addtime = addtime;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	/**
	 * 设置：材料编号
	 */
	public void setMaterialcode(String materialcode) {
		this.materialcode = materialcode;
	}
	/**
	 * 获取：材料编号
	 */
	public String getMaterialcode() {
		return materialcode;
	}
	
	/**
	 * 设置：材料名称
	 */
	public void setMaterialname(String materialname) {
		this.materialname = materialname;
	}
	/**
	 * 获取：材料名称
	 */
	public String getMaterialname() {
		return materialname;
	}
	
	/**
	 * 设置：材料类别
	 */
	public void setMaterialcategory(String materialcategory) {
		this.materialcategory = materialcategory;
	}
	/**
	 * 获取：材料类别
	 */
	public String getMaterialcategory() {
		return materialcategory;
	}
	
	/**
	 * 设置：材料品牌
	 */
	public void setMaterialbrand(String materialbrand) {
		this.materialbrand = materialbrand;
	}
	/**
	 * 获取：材料品牌
	 */
	public String getMaterialbrand() {
		return materialbrand;
	}
	
	/**
	 * 设置：材料规格
	 */
	public void setMaterialspec(String materialspec) {
		this.materialspec = materialspec;
	}
	/**
	 * 获取：材料规格
	 */
	public String getMaterialspec() {
		return materialspec;
	}
	
	/**
	 * 设置：材料单位
	 */
	public void setMaterialunit(String materialunit) {
		this.materialunit = materialunit;
	}
	/**
	 * 获取：材料单位
	 */
	public String getMaterialunit() {
		return materialunit;
	}
	
	/**
	 * 设置：材料单价
	 */
	public void setMaterialprice(Double materialprice) {
		this.materialprice = materialprice;
	}
	/**
	 * 获取：材料单价
	 */
	public Double getMaterialprice() {
		return materialprice;
	}
	
	/**
	 * 设置：材料图片
	 */
	public void setMaterialimage(String materialimage) {
		this.materialimage = materialimage;
	}
	/**
	 * 获取：材料图片
	 */
	public String getMaterialimage() {
		return materialimage;
	}
	
	/**
	 * 设置：材料描述
	 */
	public void setMaterialdescription(String materialdescription) {
		this.materialdescription = materialdescription;
	}
	/**
	 * 获取：材料描述
	 */
	public String getMaterialdescription() {
		return materialdescription;
	}
	
	/**
	 * 设置：供应商
	 */
	public void setSupplier(String supplier) {
		this.supplier = supplier;
	}
	/**
	 * 获取：供应商
	 */
	public String getSupplier() {
		return supplier;
	}
	
	/**
	 * 设置：供应商联系方式
	 */
	public void setSuppliercontact(String suppliercontact) {
		this.suppliercontact = suppliercontact;
	}
	/**
	 * 获取：供应商联系方式
	 */
	public String getSuppliercontact() {
		return suppliercontact;
	}
	
	/**
	 * 设置：材料质量等级
	 */
	public void setQualitygrade(String qualitygrade) {
		this.qualitygrade = qualitygrade;
	}
	/**
	 * 获取：材料质量等级
	 */
	public String getQualitygrade() {
		return qualitygrade;
	}
	
	/**
	 * 设置：环保等级
	 */
	public void setEnvironmentalgrade(String environmentalgrade) {
		this.environmentalgrade = environmentalgrade;
	}
	/**
	 * 获取：环保等级
	 */
	public String getEnvironmentalgrade() {
		return environmentalgrade;
	}
	
	/**
	 * 设置：防火等级
	 */
	public void setFiregrade(String firegrade) {
		this.firegrade = firegrade;
	}
	/**
	 * 获取：防火等级
	 */
	public String getFiregrade() {
		return firegrade;
	}
	
	/**
	 * 设置：使用寿命
	 */
	public void setServicelife(Integer servicelife) {
		this.servicelife = servicelife;
	}
	/**
	 * 获取：使用寿命
	 */
	public Integer getServicelife() {
		return servicelife;
	}
	
	/**
	 * 设置：维护周期
	 */
	public void setMaintenancecycle(Integer maintenancecycle) {
		this.maintenancecycle = maintenancecycle;
	}
	/**
	 * 获取：维护周期
	 */
	public Integer getMaintenancecycle() {
		return maintenancecycle;
	}

	/**
	 * 设置：适用部位
	 */
	public void setApplicableparts(String applicableparts) {
		this.applicableparts = applicableparts;
	}
	/**
	 * 获取：适用部位
	 */
	public String getApplicableparts() {
		return applicableparts;
	}

	/**
	 * 设置：技术参数
	 */
	public void setTechnicalparameters(String technicalparameters) {
		this.technicalparameters = technicalparameters;
	}
	/**
	 * 获取：技术参数
	 */
	public String getTechnicalparameters() {
		return technicalparameters;
	}

	/**
	 * 设置：安装要求
	 */
	public void setInstallationrequirements(String installationrequirements) {
		this.installationrequirements = installationrequirements;
	}
	/**
	 * 获取：安装要求
	 */
	public String getInstallationrequirements() {
		return installationrequirements;
	}

	/**
	 * 设置：库存数量
	 */
	public void setStockquantity(Integer stockquantity) {
		this.stockquantity = stockquantity;
	}
	/**
	 * 获取：库存数量
	 */
	public Integer getStockquantity() {
		return stockquantity;
	}

	/**
	 * 设置：最小库存
	 */
	public void setMinstock(Integer minstock) {
		this.minstock = minstock;
	}
	/**
	 * 获取：最小库存
	 */
	public Integer getMinstock() {
		return minstock;
	}

	/**
	 * 设置：是否启用
	 */
	public void setIsenabled(String isenabled) {
		this.isenabled = isenabled;
	}
	/**
	 * 获取：是否启用
	 */
	public String getIsenabled() {
		return isenabled;
	}
}
