package com.cl.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Map;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

import com.cl.utils.ValidatorUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;

import com.cl.entity.JianzhudesignEntity;
import com.cl.entity.view.JianzhudesignView;
import com.cl.service.JianzhudesignService;
import com.cl.service.TokenService;
import com.cl.utils.PageUtils;
import com.cl.utils.R;
import com.cl.utils.MPUtil;
import com.cl.utils.CommonUtil;
import java.io.IOException;

/**
 * 建筑设计
 * 后端接口
 * <AUTHOR> @email 
 * @date 2024-01-25 13:22:32
 */
@RestController
@RequestMapping("/jianzhudesign")
public class JianzhudesignController {
    @Autowired
    private JianzhudesignService jianzhudesignService;

    @Autowired
    private TokenService tokenService;

    /**
     * 登录
     */
    @RequestMapping(value = "/login")
    public R login(String username, String password, String captcha, HttpServletRequest request) {
        return R.error("请使用管理员或其他用户类型登录");
    }

    /**
     * 注册
     */
    @RequestMapping(value = "/register")
    public R register(@RequestBody JianzhudesignEntity jianzhudesign, HttpServletRequest request) {
        return R.error("不支持注册功能");
    }

    /**
     * 退出
     */
    @RequestMapping(value = "/logout")
    public R logout(HttpServletRequest request) {
        request.getSession().invalidate();
        return R.ok("退出成功");
    }

    /**
     * 密码重置
     */
    @RequestMapping(value = "/resetPass")
    public R resetPass(String username, HttpServletRequest request) {
        return R.error("不支持密码重置功能");
    }

    /**
     * 列表
     */
    @RequestMapping("/page")
    public R page(@RequestParam Map<String, Object> params, JianzhudesignEntity jianzhudesign,
                  HttpServletRequest request) {
        EntityWrapper<JianzhudesignEntity> ew = new EntityWrapper<JianzhudesignEntity>();

        PageUtils page = jianzhudesignService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.likeOrEq(ew, jianzhudesign), params), params));

        return R.ok().put("data", page);
    }

    /**
     * 列表
     */
    @RequestMapping("/list")
    public R list(JianzhudesignEntity jianzhudesign) {
        EntityWrapper<JianzhudesignEntity> ew = new EntityWrapper<JianzhudesignEntity>();
        ew.allEq(MPUtil.allEQMapPre(jianzhudesign, "jianzhudesign"));
        return R.ok().put("data", jianzhudesignService.selectListView(ew));
    }

    /**
     * 查询
     */
    @RequestMapping("/query")
    public R query(JianzhudesignEntity jianzhudesign) {
        EntityWrapper<JianzhudesignEntity> ew = new EntityWrapper<JianzhudesignEntity>();
        ew.allEq(MPUtil.allEQMapPre(jianzhudesign, "jianzhudesign"));
        JianzhudesignView jianzhudesignView = jianzhudesignService.selectView(ew);
        return R.ok("查询建筑设计成功").put("data", jianzhudesignView);
    }

    /**
     * 后端详情
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id) {
        JianzhudesignEntity jianzhudesign = jianzhudesignService.selectById(id);
        return R.ok().put("data", jianzhudesign);
    }

    /**
     * 前端详情
     */
    @RequestMapping("/detail/{id}")
    public R detail(@PathVariable("id") Long id) {
        JianzhudesignEntity jianzhudesign = jianzhudesignService.selectById(id);
        return R.ok().put("data", jianzhudesign);
    }

    /**
     * 后端保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody JianzhudesignEntity jianzhudesign, HttpServletRequest request) {
        jianzhudesign.setId(new Date().getTime() + new Double(Math.floor(Math.random() * 1000)).longValue());
        //ValidatorUtils.validateEntity(jianzhudesign);
        jianzhudesignService.insert(jianzhudesign);
        return R.ok();
    }

    /**
     * 前端保存
     */
    @RequestMapping("/add")
    public R add(@RequestBody JianzhudesignEntity jianzhudesign, HttpServletRequest request) {
        jianzhudesign.setId(new Date().getTime() + new Double(Math.floor(Math.random() * 1000)).longValue());
        //ValidatorUtils.validateEntity(jianzhudesign);
        jianzhudesignService.insert(jianzhudesign);
        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @Transactional
    public R update(@RequestBody JianzhudesignEntity jianzhudesign, HttpServletRequest request) {
        //ValidatorUtils.validateEntity(jianzhudesign);
        jianzhudesignService.updateById(jianzhudesign);//全部更新
        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids) {
        jianzhudesignService.deleteBatchIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 提醒接口
     */
    @RequestMapping("/remind/{columnName}/{type}")
    public R remindCount(@PathVariable("columnName") String columnName, HttpServletRequest request,
                         @PathVariable("type") String type, @RequestParam Map<String, Object> map) {
        map.put("column", columnName);
        map.put("type", type);

        if (type.equals("2")) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar c = Calendar.getInstance();
            Date remindStartDate = null;
            Date remindEndDate = null;
            if (map.get("remindstart") != null) {
                Integer remindStart = Integer.parseInt(map.get("remindstart").toString());
                c.setTime(new Date());
                c.add(Calendar.DAY_OF_MONTH, remindStart);
                remindStartDate = c.getTime();
                map.put("remindstart", sdf.format(remindStartDate));
            }
            if (map.get("remindend") != null) {
                Integer remindEnd = Integer.parseInt(map.get("remindend").toString());
                c.setTime(new Date());
                c.add(Calendar.DAY_OF_MONTH, remindEnd);
                remindEndDate = c.getTime();
                map.put("remindend", sdf.format(remindEndDate));
            }
        }

        Wrapper<JianzhudesignEntity> wrapper = new EntityWrapper<JianzhudesignEntity>();
        if (map.get("remindstart") != null) {
            wrapper.ge(columnName, map.get("remindstart"));
        }
        if (map.get("remindend") != null) {
            wrapper.le(columnName, map.get("remindend"));
        }

        int count = jianzhudesignService.selectCount(wrapper);
        return R.ok().put("count", count);
    }

    /**
     * 审核
     */
    @RequestMapping("/audit")
    public R audit(@RequestBody JianzhudesignEntity jianzhudesign, HttpServletRequest request) {
        jianzhudesignService.updateById(jianzhudesign);
        return R.ok();
    }

    /**
     * 获取建筑类型统计
     */
    @RequestMapping("/buildingTypeStats")
    public R buildingTypeStats() {
        List<Map<String, Object>> stats = jianzhudesignService.getBuildingTypeStats();
        return R.ok().put("data", stats);
    }

    /**
     * 获取设计师工作量统计
     */
    @RequestMapping("/designerWorkloadStats")
    public R designerWorkloadStats() {
        List<Map<String, Object>> stats = jianzhudesignService.getDesignerWorkloadStats();
        return R.ok().put("data", stats);
    }

    /**
     * 获取月度设计数量统计
     */
    @RequestMapping("/monthlyDesignStats")
    public R monthlyDesignStats() {
        List<Map<String, Object>> stats = jianzhudesignService.getMonthlyDesignStats();
        return R.ok().put("data", stats);
    }
}
