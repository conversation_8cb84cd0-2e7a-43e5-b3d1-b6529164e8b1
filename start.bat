@echo off
echo 正在启动建筑工程项目管理系统...
echo.
echo 注意：
echo 1. 请确保MySQL数据库已启动
echo 2. 数据库配置在 src/main/resources/config.properties 中
echo 3. 默认数据库连接：*************************************
echo 4. 默认用户名：root，密码：123456
echo.

REM 使用简单的HTTP服务器来提供静态文件
echo 启动静态文件服务器...
cd src/main/webapp
start "静态文件服务器" cmd /k "npx http-server -p 8080 -c-1"

echo.
echo 项目已启动！
echo.
echo 访问地址：
echo 主页：http://localhost:8080
echo 管理后台：http://localhost:8080/manage/index.html
echo 前台页面：http://localhost:8080/client/index.html
echo.
echo 注意：当前只启动了前端静态文件，后端API需要数据库支持
echo 如需完整功能，请配置MySQL数据库并导入数据
echo.
pause
