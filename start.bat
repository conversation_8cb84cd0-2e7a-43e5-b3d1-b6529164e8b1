@echo off
chcp 65001 >nul
echo ========================================
echo    建筑工程项目管理系统 - 增强版
echo ========================================
echo.
echo 正在启动系统...
echo.
echo 系统功能模块：
echo ✓ 项目信息管理
echo ✓ 建筑设计管理 (新增)
echo ✓ 建筑材料库 (新增)
echo ✓ 项目进度管理 (新增)
echo ✓ 质量监控 (新增)
echo ✓ 现代化仪表板 (新增)
echo.
echo 数据库配置：
echo - 数据库名：cl3841596_enhanced
echo - 连接地址：**********************************************
echo - 用户名：root
echo - 密码：123456
echo.
echo 注意事项：
echo 1. 请确保MySQL数据库服务已启动
echo 2. 建议先导入 database/enhanced_schema.sql 创建增强版数据库
echo 3. 如需完整后端功能，请配置数据库连接
echo.

REM 检查Node.js是否安装
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo 警告：未检测到Node.js，将使用Python启动服务器
    goto :python_server
)

REM 检查http-server是否安装
where http-server >nul 2>nul
if %errorlevel% neq 0 (
    echo 正在安装http-server...
    npm install -g http-server
)

echo 启动静态文件服务器...
cd src/main/webapp
start "建筑工程管理系统" cmd /k "http-server -p 8080 -c-1 --cors"
goto :success

:python_server
echo 使用Python启动服务器...
cd src/main/webapp
start "建筑工程管理系统" cmd /k "python -m http.server 8080"

:success
echo.
echo ========================================
echo           系统启动成功！
echo ========================================
echo.
echo 🌐 访问地址：
echo   主页目录：http://localhost:8080
echo   系统仪表板：http://localhost:8080/manage/pages/dashboard/index.html
echo   建筑设计：http://localhost:8080/manage/pages/jianzhudesign/index.html
echo   材料管理：http://localhost:8080/manage/pages/buildingmaterial/index.html
echo   管理后台：http://localhost:8080/manage/index.html
echo   前台页面：http://localhost:8080/client/index.html
echo.
echo 🔧 系统特色：
echo   ✨ 现代化UI设计
echo   📊 数据可视化仪表板
echo   🏗️ 详细建筑设计管理
echo   📦 智能材料库存管理
echo   📈 项目进度跟踪
echo   🔍 质量监控体系
echo.
echo 💡 使用提示：
echo   - 当前为前端演示版本
echo   - 完整功能需要配置数据库
echo   - 建议使用Chrome或Edge浏览器
echo.
echo 按任意键打开系统主页...
pause >nul
start http://localhost:8080/manage/pages/dashboard/index.html
