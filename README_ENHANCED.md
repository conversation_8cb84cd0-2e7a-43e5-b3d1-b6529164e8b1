# 建筑工程项目管理系统 - 增强版

## 🏗️ 项目简介

这是一个现代化的建筑工程项目管理系统，专为建筑行业设计，提供全面的项目管理、设计管理、材料管理、进度跟踪和质量监控功能。

## ✨ 系统特色

### 🎨 现代化UI设计
- 响应式设计，支持多设备访问
- 渐变色彩搭配，视觉效果出色
- 卡片式布局，信息展示清晰
- 动画效果，提升用户体验

### 📊 数据可视化
- ECharts图表展示项目数据
- 实时统计仪表板
- 多维度数据分析
- 趋势预测和预警

### 🏗️ 建筑设计管理
- 3D模型文件管理
- CAD图纸集成
- 效果图展示
- 设计版本控制
- 审核流程管理

### 📦 智能材料库
- 材料分类管理
- 库存预警系统
- 供应商信息管理
- 价格趋势分析
- 材料推荐算法

## 🚀 功能模块

### 1. 项目仪表板
- **核心指标展示**：项目数量、设计方案、总预算、平均进度
- **图表分析**：项目进度概览、建筑类型分布
- **快速操作**：新建项目、新建设计、添加材料、生成报告
- **待办事项**：任务管理和提醒
- **通知中心**：系统消息和预警

### 2. 建筑设计管理
- **设计信息**：编号、名称、类型、风格、参数
- **文件管理**：设计图纸、3D模型、CAD文件、效果图
- **材料规划**：主要材料、外墙材料、屋顶材料、门窗材料
- **审核流程**：设计审核、意见反馈、版本控制
- **多视图展示**：表格视图、卡片视图、画廊视图

### 3. 建筑材料库
- **材料档案**：编号、名称、规格、品牌、价格
- **库存管理**：数量跟踪、预警设置、自动补货
- **供应商管理**：联系方式、评价体系、合作历史
- **质量控制**：等级评定、技术参数、检测报告
- **智能推荐**：根据项目类型推荐合适材料

### 4. 项目进度管理
- **阶段规划**：里程碑设置、关键路径分析
- **进度跟踪**：计划vs实际、进度预警
- **资源管理**：人员分配、设备调度
- **成本控制**：预算跟踪、成本分析
- **风险评估**：风险识别、应对措施

### 5. 质量监控
- **检查管理**：检查计划、标准制定、结果记录
- **问题跟踪**：问题发现、整改要求、复查验证
- **质量评价**：等级评定、合格率统计
- **经验总结**：最佳实践、改进建议

## 🛠️ 技术架构

### 后端技术
- **框架**：Spring Boot + Spring MVC
- **数据库**：MySQL 8.0
- **ORM**：MyBatis Plus
- **构建工具**：Maven
- **Java版本**：JDK 8+

### 前端技术
- **框架**：Vue.js 2.x
- **UI组件**：Element UI
- **图表库**：ECharts 5.x
- **图标库**：Font Awesome 6.x
- **样式**：CSS3 + 渐变设计

### 数据库设计
- **项目信息表**：xiangmuxinxi (增强)
- **建筑设计表**：jianzhudesign (新增)
- **建筑材料表**：buildingmaterial (新增)
- **项目进度表**：projectprogress (新增)
- **质量监控表**：qualitymonitor (新增)

## 📦 安装部署

### 环境要求
- JDK 8 或更高版本
- MySQL 8.0 或更高版本
- Maven 3.6+
- Node.js (可选，用于前端开发)

### 快速启动

1. **克隆项目**
```bash
git clone [项目地址]
cd springboot146-main
```

2. **数据库配置**
```sql
-- 创建数据库
CREATE DATABASE cl3841596_enhanced;

-- 导入数据库结构
mysql -u root -p cl3841596_enhanced < database/enhanced_schema.sql
```

3. **配置文件**
编辑 `src/main/resources/config.properties`：
```properties
jdbc.url=**********************************************
jdbc.username=root
jdbc.password=123456
```

4. **启动系统**
```bash
# Windows
start.bat

# Linux/Mac
./start.sh
```

5. **访问系统**
- 系统仪表板：http://localhost:8080/manage/pages/dashboard/index.html
- 建筑设计：http://localhost:8080/manage/pages/jianzhudesign/index.html
- 材料管理：http://localhost:8080/manage/pages/buildingmaterial/index.html

## 🎯 系统优化亮点

### 界面优化
- ✅ 现代化渐变色设计
- ✅ 响应式布局适配
- ✅ 卡片式信息展示
- ✅ 动画过渡效果
- ✅ 图标字体集成

### 功能增强
- ✅ 建筑设计详细管理
- ✅ 3D模型文件支持
- ✅ CAD图纸集成
- ✅ 材料库存管理
- ✅ 项目进度跟踪
- ✅ 质量监控体系
- ✅ 数据可视化仪表板

### 技术提升
- ✅ Vue.js组件化开发
- ✅ ECharts图表集成
- ✅ Element UI组件库
- ✅ 数据库结构优化
- ✅ RESTful API设计

## 🔧 开发指南

### 项目结构
```
springboot146-main/
├── src/main/java/com/cl/
│   ├── entity/          # 实体类
│   ├── controller/      # 控制器
│   ├── service/         # 服务层
│   └── dao/            # 数据访问层
├── src/main/webapp/
│   ├── manage/         # 管理后台
│   │   └── pages/      # 功能页面
│   └── client/         # 前台页面
├── database/           # 数据库脚本
└── docs/              # 文档资料
```

### 新增功能模块

1. **创建实体类**
```java
@TableName("table_name")
public class EntityName {
    // 字段定义
}
```

2. **创建控制器**
```java
@RestController
@RequestMapping("/api/module")
public class ModuleController {
    // API接口
}
```

3. **创建前端页面**
```html
<!-- 页面模板 -->
<div id="app">
    <!-- Vue.js组件 -->
</div>
```

## 📱 系统演示

### 启动方式
1. 运行 `start.bat` (Windows) 或 `start.sh` (Linux/Mac)
2. 访问 http://localhost:8080
3. 体验各个功能模块

### 主要页面
- **仪表板**：http://localhost:8080/manage/pages/dashboard/index.html
- **建筑设计**：http://localhost:8080/manage/pages/jianzhudesign/index.html
- **材料管理**：http://localhost:8080/manage/pages/buildingmaterial/index.html

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

**建筑工程项目管理系统 - 让建筑项目管理更智能、更高效！** 🏗️✨

### 系统特点总结
- 🎨 **现代化UI**：渐变色彩、卡片布局、动画效果
- 📊 **数据可视化**：ECharts图表、实时统计、趋势分析
- 🏗️ **建筑专业**：3D模型、CAD集成、材料管理
- 📈 **项目管理**：进度跟踪、成本控制、质量监控
- 🔧 **技术先进**：Vue.js、Element UI、Spring Boot
