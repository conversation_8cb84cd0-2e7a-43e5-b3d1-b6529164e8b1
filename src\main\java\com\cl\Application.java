package com.cl;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

/**
 * Spring Boot启动类
 */
@SpringBootApplication
public class Application extends SpringBootServletInitializer {

    public static void main(String[] args) {
        System.out.println("正在启动Spring Boot应用...");
        SpringApplication.run(Application.class, args);
        System.out.println("=================================");
        System.out.println("项目启动成功！");
        System.out.println("访问地址：http://localhost:8080");
        System.out.println("管理后台：http://localhost:8080/manage/index.html");
        System.out.println("前台页面：http://localhost:8080/client/index.html");
        System.out.println("=================================");
    }
}
