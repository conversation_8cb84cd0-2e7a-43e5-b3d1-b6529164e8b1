package com.cl;

import org.apache.catalina.Context;
import org.apache.catalina.LifecycleException;
import org.apache.catalina.startup.Tomcat;
import org.apache.catalina.webresources.DirResourceSet;
import org.apache.catalina.webresources.StandardRoot;

import java.io.File;

/**
 * 嵌入式Tomcat启动类
 */
public class Application {

    public static void main(String[] args) {
        try {
            String webappDirLocation = "src/main/webapp/";
            Tomcat tomcat = new Tomcat();

            // 设置端口号，默认为8080
            String webPort = System.getenv("PORT");
            if (webPort == null || webPort.isEmpty()) {
                webPort = "8080";
            }

            tomcat.setPort(Integer.valueOf(webPort));
            tomcat.setBaseDir(".");

            // 添加webapp
            Context context = tomcat.addWebapp("", new File(webappDirLocation).getAbsolutePath());
            System.out.println("configuring app with basedir: " + new File("./" + webappDirLocation).getAbsolutePath());

            // 声明一个文件夹，用来存放编译后的类文件
            File additionWebInfClasses = new File("target/classes");
            StandardRoot resources = new StandardRoot(context);
            resources.addPreResources(new DirResourceSet(resources, "/WEB-INF/classes",
                    additionWebInfClasses.getAbsolutePath(), "/"));
            context.setResources(resources);

            tomcat.start();
            System.out.println("项目启动成功！");
            System.out.println("访问地址：http://localhost:" + webPort);
            System.out.println("管理后台：http://localhost:" + webPort + "/manage/index.html");
            System.out.println("前台页面：http://localhost:" + webPort + "/client/index.html");

            tomcat.getServer().await();
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("启动失败: " + e.getMessage());
        }
    }
}
