<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>建筑设计管理 - 建筑工程项目管理系统</title>
    <link rel="stylesheet" href="../../css/element-ui.css">
    <link rel="stylesheet" href="../../css/common.css">
    <link rel="stylesheet" href="./design.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="app">
        <div class="design-container">
            <!-- 顶部导航栏 -->
            <div class="top-nav">
                <div class="nav-left">
                    <h1><i class="fas fa-drafting-compass"></i> 建筑设计管理</h1>
                </div>
                <div class="nav-right">
                    <el-button type="primary" icon="el-icon-plus" @click="showAddDialog">新建设计</el-button>
                    <el-button type="success" icon="el-icon-upload2" @click="showImportDialog">批量导入</el-button>
                    <el-button type="info" icon="el-icon-download" @click="exportData">导出数据</el-button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-cards">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{totalDesigns}}</h3>
                        <p>总设计数量</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{pendingDesigns}}</h3>
                        <p>待审核设计</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{approvedDesigns}}</h3>
                        <p>已通过设计</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-content">
                        <h3>¥{{totalBudget}}</h3>
                        <p>总预算金额</p>
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="search-filter">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-input
                            v-model="searchForm.keyword"
                            placeholder="搜索设计名称、编号..."
                            prefix-icon="el-icon-search"
                            @input="handleSearch">
                        </el-input>
                    </el-col>
                    <el-col :span="4">
                        <el-select v-model="searchForm.buildingtype" placeholder="建筑类型" @change="handleSearch">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="住宅建筑" value="住宅建筑"></el-option>
                            <el-option label="商业建筑" value="商业建筑"></el-option>
                            <el-option label="办公建筑" value="办公建筑"></el-option>
                            <el-option label="工业建筑" value="工业建筑"></el-option>
                            <el-option label="公共建筑" value="公共建筑"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <el-select v-model="searchForm.auditstatus" placeholder="审核状态" @change="handleSearch">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="待审核" value="待审核"></el-option>
                            <el-option label="已通过" value="已通过"></el-option>
                            <el-option label="已拒绝" value="已拒绝"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <el-select v-model="searchForm.designer" placeholder="设计师" @change="handleSearch">
                            <el-option label="全部" value=""></el-option>
                            <el-option v-for="designer in designerList" :key="designer" :label="designer" :value="designer"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="6">
                        <el-date-picker
                            v-model="searchForm.dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="handleSearch">
                        </el-date-picker>
                    </el-col>
                </el-row>
            </div>

            <!-- 视图切换 -->
            <div class="view-toggle">
                <el-radio-group v-model="viewMode" @change="changeViewMode">
                    <el-radio-button label="table"><i class="el-icon-s-grid"></i> 表格视图</el-radio-button>
                    <el-radio-button label="card"><i class="el-icon-s-order"></i> 卡片视图</el-radio-button>
                    <el-radio-button label="gallery"><i class="el-icon-picture"></i> 画廊视图</el-radio-button>
                </el-radio-group>
            </div>

            <!-- 表格视图 -->
            <div v-if="viewMode === 'table'" class="table-view">
                <el-table
                    :data="designList"
                    style="width: 100%"
                    :loading="loading"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column prop="designcode" label="设计编号" width="120"></el-table-column>
                    <el-table-column prop="designname" label="设计名称" width="200" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="buildingtype" label="建筑类型" width="100"></el-table-column>
                    <el-table-column prop="architecturalstyle" label="建筑风格" width="120"></el-table-column>
                    <el-table-column prop="buildingarea" label="建筑面积" width="100">
                        <template slot-scope="scope">
                            {{scope.row.buildingarea}}㎡
                        </template>
                    </el-table-column>
                    <el-table-column prop="floors" label="层数" width="80"></el-table-column>
                    <el-table-column prop="designer" label="设计师" width="100"></el-table-column>
                    <el-table-column prop="designdate" label="设计日期" width="120"></el-table-column>
                    <el-table-column prop="auditstatus" label="审核状态" width="100">
                        <template slot-scope="scope">
                            <el-tag :type="getStatusType(scope.row.auditstatus)">
                                {{scope.row.auditstatus}}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="budgetamount" label="预算金额" width="120">
                        <template slot-scope="scope">
                            ¥{{formatMoney(scope.row.budgetamount)}}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200" fixed="right">
                        <template slot-scope="scope">
                            <el-button size="mini" @click="viewDesign(scope.row)">查看</el-button>
                            <el-button size="mini" type="primary" @click="editDesign(scope.row)">编辑</el-button>
                            <el-button size="mini" type="success" @click="auditDesign(scope.row)" v-if="scope.row.auditstatus === '待审核'">审核</el-button>
                            <el-button size="mini" type="danger" @click="deleteDesign(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 卡片视图 -->
            <div v-if="viewMode === 'card'" class="card-view">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="design in designList" :key="design.id">
                        <div class="design-card">
                            <div class="card-header">
                                <h3>{{design.designname}}</h3>
                                <el-tag :type="getStatusType(design.auditstatus)">{{design.auditstatus}}</el-tag>
                            </div>
                            <div class="card-image">
                                <img :src="design.renderingimages || '/images/default-building.jpg'" alt="效果图">
                                <div class="image-overlay">
                                    <el-button type="primary" icon="el-icon-view" circle @click="viewDesign(design)"></el-button>
                                    <el-button type="success" icon="el-icon-edit" circle @click="editDesign(design)"></el-button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="design-info">
                                    <p><i class="fas fa-code"></i> {{design.designcode}}</p>
                                    <p><i class="fas fa-building"></i> {{design.buildingtype}}</p>
                                    <p><i class="fas fa-ruler-combined"></i> {{design.buildingarea}}㎡</p>
                                    <p><i class="fas fa-layer-group"></i> {{design.floors}}层</p>
                                    <p><i class="fas fa-user"></i> {{design.designer}}</p>
                                    <p><i class="fas fa-dollar-sign"></i> ¥{{formatMoney(design.budgetamount)}}</p>
                                </div>
                            </div>
                            <div class="card-actions">
                                <el-button size="small" @click="viewDesign(design)">详情</el-button>
                                <el-button size="small" type="primary" @click="editDesign(design)">编辑</el-button>
                                <el-button size="small" type="danger" @click="deleteDesign(design)">删除</el-button>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total">
                </el-pagination>
            </div>
        </div>

        <!-- 新建/编辑设计对话框 -->
        <el-dialog
            :title="dialogTitle"
            :visible.sync="dialogVisible"
            width="80%"
            :before-close="handleClose">
            <design-form
                ref="designForm"
                :form-data="currentDesign"
                :is-edit="isEdit"
                @submit="handleSubmit"
                @cancel="handleClose">
            </design-form>
        </el-dialog>

        <!-- 设计详情对话框 -->
        <el-dialog
            title="设计详情"
            :visible.sync="detailVisible"
            width="90%"
            :before-close="closeDetail">
            <design-detail
                :design-data="currentDesign"
                @edit="editDesign"
                @audit="auditDesign">
            </design-detail>
        </el-dialog>
    </div>

    <script src="../../js/vue.js"></script>
    <script src="../../js/element-ui.js"></script>
    <script src="../../js/axios.js"></script>
    <script src="./components/design-form.js"></script>
    <script src="./components/design-detail.js"></script>
    <script src="./design.js"></script>
</body>
</html>
