package com.cl.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.lang.reflect.InvocationTargetException;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.beanutils.BeanUtils;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.enums.FieldFill;
import com.baomidou.mybatisplus.enums.IdType;

/**
 * 质量监控
 * 数据库通用操作实体类（普通增删改查）
 * <AUTHOR> @email 
 * @date 2024-01-25 13:22:32
 */
@TableName("qualitymonitor")
public class QualitymonitorEntity<T> implements Serializable {
	private static final long serialVersionUID = 1L;

	public QualitymonitorEntity() {
		
	}
	
	public QualitymonitorEntity(T t) {
		try {
			BeanUtils.copyProperties(this, t);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	/**
	 * 主键id
	 */
	@TableId
	private Long id;
	
	/**
	 * 检查编号
	 */
	private String checkcode;
	
	/**
	 * 项目编号
	 */
	private String xiangmubianhao;
	
	/**
	 * 项目名称
	 */
	private String xiangmumingcheng;
	
	/**
	 * 检查类型
	 */
	private String checktype;
	
	/**
	 * 检查阶段
	 */
	private String checkstage;
	
	/**
	 * 检查部位
	 */
	private String checkpart;
	
	/**
	 * 检查内容
	 */
	private String checkcontent;
	
	/**
	 * 检查标准
	 */
	private String checkstandard;
	
	/**
	 * 检查方法
	 */
	private String checkmethod;
	
	/**
	 * 检查日期
	 */
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat 		
	private Date checkdate;
	
	/**
	 * 检查人员
	 */
	private String checkperson;
	
	/**
	 * 陪同人员
	 */
	private String accompanyperson;
	
	/**
	 * 检查结果
	 */
	private String checkresult;
	
	/**
	 * 质量等级
	 */
	private String qualitygrade;
	
	/**
	 * 合格率
	 */
	private Double passrate;
	
	/**
	 * 发现问题
	 */
	private String foundissues;
	
	/**
	 * 问题等级
	 */
	private String issuelevel;
	
	/**
	 * 整改要求
	 */
	private String rectificationrequirements;
	
	/**
	 * 整改期限
	 */
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat 		
	private Date rectificationdeadline;
	
	/**
	 * 整改负责人
	 */
	private String rectificationperson;
	
	/**
	 * 整改情况
	 */
	private String rectificationstatus;
	
	/**
	 * 复查日期
	 */
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat 		
	private Date recheckdate;
	
	/**
	 * 复查结果
	 */
	private String recheckresult;
	
	/**
	 * 复查人员
	 */
	private String recheckperson;
	
	/**
	 * 检查照片
	 */
	private String checkphotos;
	
	/**
	 * 检查报告
	 */
	private String checkreport;
	
	/**
	 * 整改照片
	 */
	private String rectificationphotos;
	
	/**
	 * 处罚措施
	 */
	private String penaltymeasures;
	
	/**
	 * 处罚金额
	 */
	private Double penaltyamount;
	
	/**
	 * 奖励措施
	 */
	private String rewardmeasures;
	
	/**
	 * 奖励金额
	 */
	private Double rewardamount;
	
	/**
	 * 经验总结
	 */
	private String experiencesummary;
	
	/**
	 * 改进建议
	 */
	private String improvementsuggestions;
	
	/**
	 * 监理意见
	 */
	private String supervisoropinion;
	
	/**
	 * 业主意见
	 */
	private String owneropinion;
	
	/**
	 * 状态
	 */
	private String status;
	
	/**
	 * 备注
	 */
	private String remarks;

	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat
	private Date addtime;

	public Date getAddtime() {
		return addtime;
	}
	public void setAddtime(Date addtime) {
		this.addtime = addtime;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	/**
	 * 设置：检查编号
	 */
	public void setCheckcode(String checkcode) {
		this.checkcode = checkcode;
	}
	/**
	 * 获取：检查编号
	 */
	public String getCheckcode() {
		return checkcode;
	}
	
	/**
	 * 设置：项目编号
	 */
	public void setXiangmubianhao(String xiangmubianhao) {
		this.xiangmubianhao = xiangmubianhao;
	}
	/**
	 * 获取：项目编号
	 */
	public String getXiangmubianhao() {
		return xiangmubianhao;
	}
	
	/**
	 * 设置：项目名称
	 */
	public void setXiangmumingcheng(String xiangmumingcheng) {
		this.xiangmumingcheng = xiangmumingcheng;
	}
	/**
	 * 获取：项目名称
	 */
	public String getXiangmumingcheng() {
		return xiangmumingcheng;
	}
	
	/**
	 * 设置：检查类型
	 */
	public void setChecktype(String checktype) {
		this.checktype = checktype;
	}
	/**
	 * 获取：检查类型
	 */
	public String getChecktype() {
		return checktype;
	}
	
	/**
	 * 设置：检查阶段
	 */
	public void setCheckstage(String checkstage) {
		this.checkstage = checkstage;
	}
	/**
	 * 获取：检查阶段
	 */
	public String getCheckstage() {
		return checkstage;
	}
	
	/**
	 * 设置：检查部位
	 */
	public void setCheckpart(String checkpart) {
		this.checkpart = checkpart;
	}
	/**
	 * 获取：检查部位
	 */
	public String getCheckpart() {
		return checkpart;
	}
	
	/**
	 * 设置：检查内容
	 */
	public void setCheckcontent(String checkcontent) {
		this.checkcontent = checkcontent;
	}
	/**
	 * 获取：检查内容
	 */
	public String getCheckcontent() {
		return checkcontent;
	}
	
	/**
	 * 设置：检查标准
	 */
	public void setCheckstandard(String checkstandard) {
		this.checkstandard = checkstandard;
	}
	/**
	 * 获取：检查标准
	 */
	public String getCheckstandard() {
		return checkstandard;
	}
	
	/**
	 * 设置：检查方法
	 */
	public void setCheckmethod(String checkmethod) {
		this.checkmethod = checkmethod;
	}
	/**
	 * 获取：检查方法
	 */
	public String getCheckmethod() {
		return checkmethod;
	}
	
	/**
	 * 设置：检查日期
	 */
	public void setCheckdate(Date checkdate) {
		this.checkdate = checkdate;
	}
	/**
	 * 获取：检查日期
	 */
	public Date getCheckdate() {
		return checkdate;
	}
	
	/**
	 * 设置：检查人员
	 */
	public void setCheckperson(String checkperson) {
		this.checkperson = checkperson;
	}
	/**
	 * 获取：检查人员
	 */
	public String getCheckperson() {
		return checkperson;
	}
	
	/**
	 * 设置：陪同人员
	 */
	public void setAccompanyperson(String accompanyperson) {
		this.accompanyperson = accompanyperson;
	}
	/**
	 * 获取：陪同人员
	 */
	public String getAccompanyperson() {
		return accompanyperson;
	}
	
	/**
	 * 设置：检查结果
	 */
	public void setCheckresult(String checkresult) {
		this.checkresult = checkresult;
	}
	/**
	 * 获取：检查结果
	 */
	public String getCheckresult() {
		return checkresult;
	}
	
	/**
	 * 设置：质量等级
	 */
	public void setQualitygrade(String qualitygrade) {
		this.qualitygrade = qualitygrade;
	}
	/**
	 * 获取：质量等级
	 */
	public String getQualitygrade() {
		return qualitygrade;
	}
	
	/**
	 * 设置：合格率
	 */
	public void setPassrate(Double passrate) {
		this.passrate = passrate;
	}
	/**
	 * 获取：合格率
	 */
	public Double getPassrate() {
		return passrate;
	}

	/**
	 * 设置：发现问题
	 */
	public void setFoundissues(String foundissues) {
		this.foundissues = foundissues;
	}
	/**
	 * 获取：发现问题
	 */
	public String getFoundissues() {
		return foundissues;
	}

	/**
	 * 设置：问题等级
	 */
	public void setIssuelevel(String issuelevel) {
		this.issuelevel = issuelevel;
	}
	/**
	 * 获取：问题等级
	 */
	public String getIssuelevel() {
		return issuelevel;
	}

	/**
	 * 设置：整改要求
	 */
	public void setRectificationrequirements(String rectificationrequirements) {
		this.rectificationrequirements = rectificationrequirements;
	}
	/**
	 * 获取：整改要求
	 */
	public String getRectificationrequirements() {
		return rectificationrequirements;
	}

	/**
	 * 设置：整改期限
	 */
	public void setRectificationdeadline(Date rectificationdeadline) {
		this.rectificationdeadline = rectificationdeadline;
	}
	/**
	 * 获取：整改期限
	 */
	public Date getRectificationdeadline() {
		return rectificationdeadline;
	}

	/**
	 * 设置：整改负责人
	 */
	public void setRectificationperson(String rectificationperson) {
		this.rectificationperson = rectificationperson;
	}
	/**
	 * 获取：整改负责人
	 */
	public String getRectificationperson() {
		return rectificationperson;
	}

	/**
	 * 设置：整改情况
	 */
	public void setRectificationstatus(String rectificationstatus) {
		this.rectificationstatus = rectificationstatus;
	}
	/**
	 * 获取：整改情况
	 */
	public String getRectificationstatus() {
		return rectificationstatus;
	}

	/**
	 * 设置：复查日期
	 */
	public void setRecheckdate(Date recheckdate) {
		this.recheckdate = recheckdate;
	}
	/**
	 * 获取：复查日期
	 */
	public Date getRecheckdate() {
		return recheckdate;
	}

	/**
	 * 设置：复查结果
	 */
	public void setRecheckresult(String recheckresult) {
		this.recheckresult = recheckresult;
	}
	/**
	 * 获取：复查结果
	 */
	public String getRecheckresult() {
		return recheckresult;
	}

	/**
	 * 设置：复查人员
	 */
	public void setRecheckperson(String recheckperson) {
		this.recheckperson = recheckperson;
	}
	/**
	 * 获取：复查人员
	 */
	public String getRecheckperson() {
		return recheckperson;
	}

	/**
	 * 设置：检查照片
	 */
	public void setCheckphotos(String checkphotos) {
		this.checkphotos = checkphotos;
	}
	/**
	 * 获取：检查照片
	 */
	public String getCheckphotos() {
		return checkphotos;
	}

	/**
	 * 设置：检查报告
	 */
	public void setCheckreport(String checkreport) {
		this.checkreport = checkreport;
	}
	/**
	 * 获取：检查报告
	 */
	public String getCheckreport() {
		return checkreport;
	}

	/**
	 * 设置：整改照片
	 */
	public void setRectificationphotos(String rectificationphotos) {
		this.rectificationphotos = rectificationphotos;
	}
	/**
	 * 获取：整改照片
	 */
	public String getRectificationphotos() {
		return rectificationphotos;
	}

	/**
	 * 设置：处罚措施
	 */
	public void setPenaltymeasures(String penaltymeasures) {
		this.penaltymeasures = penaltymeasures;
	}
	/**
	 * 获取：处罚措施
	 */
	public String getPenaltymeasures() {
		return penaltymeasures;
	}

	/**
	 * 设置：处罚金额
	 */
	public void setPenaltyamount(Double penaltyamount) {
		this.penaltyamount = penaltyamount;
	}
	/**
	 * 获取：处罚金额
	 */
	public Double getPenaltyamount() {
		return penaltyamount;
	}

	/**
	 * 设置：奖励措施
	 */
	public void setRewardmeasures(String rewardmeasures) {
		this.rewardmeasures = rewardmeasures;
	}
	/**
	 * 获取：奖励措施
	 */
	public String getRewardmeasures() {
		return rewardmeasures;
	}

	/**
	 * 设置：奖励金额
	 */
	public void setRewardamount(Double rewardamount) {
		this.rewardamount = rewardamount;
	}
	/**
	 * 获取：奖励金额
	 */
	public Double getRewardamount() {
		return rewardamount;
	}

	/**
	 * 设置：经验总结
	 */
	public void setExperiencesummary(String experiencesummary) {
		this.experiencesummary = experiencesummary;
	}
	/**
	 * 获取：经验总结
	 */
	public String getExperiencesummary() {
		return experiencesummary;
	}

	/**
	 * 设置：改进建议
	 */
	public void setImprovementsuggestions(String improvementsuggestions) {
		this.improvementsuggestions = improvementsuggestions;
	}
	/**
	 * 获取：改进建议
	 */
	public String getImprovementsuggestions() {
		return improvementsuggestions;
	}

	/**
	 * 设置：监理意见
	 */
	public void setSupervisoropinion(String supervisoropinion) {
		this.supervisoropinion = supervisoropinion;
	}
	/**
	 * 获取：监理意见
	 */
	public String getSupervisoropinion() {
		return supervisoropinion;
	}

	/**
	 * 设置：业主意见
	 */
	public void setOwneropinion(String owneropinion) {
		this.owneropinion = owneropinion;
	}
	/**
	 * 获取：业主意见
	 */
	public String getOwneropinion() {
		return owneropinion;
	}

	/**
	 * 设置：状态
	 */
	public void setStatus(String status) {
		this.status = status;
	}
	/**
	 * 获取：状态
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * 设置：备注
	 */
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	/**
	 * 获取：备注
	 */
	public String getRemarks() {
		return remarks;
	}
}
