package com.cl.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.lang.reflect.InvocationTargetException;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.beanutils.BeanUtils;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.enums.FieldFill;
import com.baomidou.mybatisplus.enums.IdType;

/**
 * 建筑设计
 * 数据库通用操作实体类（普通增删改查）
 * <AUTHOR> @email 
 * @date 2024-01-25 13:22:32
 */
@TableName("jianzhudesign")
public class JianzhudesignEntity<T> implements Serializable {
	private static final long serialVersionUID = 1L;

	public JianzhudesignEntity() {
		
	}
	
	public JianzhudesignEntity(T t) {
		try {
			BeanUtils.copyProperties(this, t);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	/**
	 * 主键id
	 */
	@TableId
	private Long id;
	
	/**
	 * 设计编号
	 */
	private String designcode;
	
	/**
	 * 设计名称
	 */
	private String designname;
	
	/**
	 * 项目编号
	 */
	private String xiangmubianhao;
	
	/**
	 * 建筑类型
	 */
	private String buildingtype;
	
	/**
	 * 建筑风格
	 */
	private String architecturalstyle;
	
	/**
	 * 建筑面积
	 */
	private Double buildingarea;
	
	/**
	 * 建筑高度
	 */
	private Double buildingheight;
	
	/**
	 * 建筑层数
	 */
	private Integer floors;
	
	/**
	 * 地下层数
	 */
	private Integer basementfloors;
	
	/**
	 * 结构类型
	 */
	private String structuretype;
	
	/**
	 * 主要材料
	 */
	private String mainmaterials;
	
	/**
	 * 外墙材料
	 */
	private String wallmaterials;
	
	/**
	 * 屋顶材料
	 */
	private String roofmaterials;
	
	/**
	 * 门窗材料
	 */
	private String windowmaterials;
	
	/**
	 * 设计图纸
	 */
	private String designdrawings;
	
	/**
	 * 3D模型文件
	 */
	private String model3dfile;
	
	/**
	 * CAD文件
	 */
	private String cadfile;
	
	/**
	 * 效果图
	 */
	private String renderingimages;
	
	/**
	 * 设计说明
	 */
	private String designdescription;
	
	/**
	 * 设计师
	 */
	private String designer;
	
	/**
	 * 设计日期
	 */
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat 		
	private Date designdate;
	
	/**
	 * 审核状态
	 */
	private String auditstatus;
	
	/**
	 * 审核意见
	 */
	private String auditcomments;
	
	/**
	 * 预算金额
	 */
	private Double budgetamount;
	
	/**
	 * 设计版本
	 */
	private String designversion;
	
	/**
	 * 是否启用
	 */
	private String isenabled;

	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat
	private Date addtime;

	public Date getAddtime() {
		return addtime;
	}
	public void setAddtime(Date addtime) {
		this.addtime = addtime;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	/**
	 * 设置：设计编号
	 */
	public void setDesigncode(String designcode) {
		this.designcode = designcode;
	}
	/**
	 * 获取：设计编号
	 */
	public String getDesigncode() {
		return designcode;
	}
	
	/**
	 * 设置：设计名称
	 */
	public void setDesignname(String designname) {
		this.designname = designname;
	}
	/**
	 * 获取：设计名称
	 */
	public String getDesignname() {
		return designname;
	}
	
	/**
	 * 设置：项目编号
	 */
	public void setXiangmubianhao(String xiangmubianhao) {
		this.xiangmubianhao = xiangmubianhao;
	}
	/**
	 * 获取：项目编号
	 */
	public String getXiangmubianhao() {
		return xiangmubianhao;
	}
	
	/**
	 * 设置：建筑类型
	 */
	public void setBuildingtype(String buildingtype) {
		this.buildingtype = buildingtype;
	}
	/**
	 * 获取：建筑类型
	 */
	public String getBuildingtype() {
		return buildingtype;
	}
	
	/**
	 * 设置：建筑风格
	 */
	public void setArchitecturalstyle(String architecturalstyle) {
		this.architecturalstyle = architecturalstyle;
	}
	/**
	 * 获取：建筑风格
	 */
	public String getArchitecturalstyle() {
		return architecturalstyle;
	}
	
	/**
	 * 设置：建筑面积
	 */
	public void setBuildingarea(Double buildingarea) {
		this.buildingarea = buildingarea;
	}
	/**
	 * 获取：建筑面积
	 */
	public Double getBuildingarea() {
		return buildingarea;
	}
	
	/**
	 * 设置：建筑高度
	 */
	public void setBuildingheight(Double buildingheight) {
		this.buildingheight = buildingheight;
	}
	/**
	 * 获取：建筑高度
	 */
	public Double getBuildingheight() {
		return buildingheight;
	}
	
	/**
	 * 设置：建筑层数
	 */
	public void setFloors(Integer floors) {
		this.floors = floors;
	}
	/**
	 * 获取：建筑层数
	 */
	public Integer getFloors() {
		return floors;
	}
	
	/**
	 * 设置：地下层数
	 */
	public void setBasementfloors(Integer basementfloors) {
		this.basementfloors = basementfloors;
	}
	/**
	 * 获取：地下层数
	 */
	public Integer getBasementfloors() {
		return basementfloors;
	}
	
	/**
	 * 设置：结构类型
	 */
	public void setStructuretype(String structuretype) {
		this.structuretype = structuretype;
	}
	/**
	 * 获取：结构类型
	 */
	public String getStructuretype() {
		return structuretype;
	}
	
	/**
	 * 设置：主要材料
	 */
	public void setMainmaterials(String mainmaterials) {
		this.mainmaterials = mainmaterials;
	}
	/**
	 * 获取：主要材料
	 */
	public String getMainmaterials() {
		return mainmaterials;
	}
	
	/**
	 * 设置：外墙材料
	 */
	public void setWallmaterials(String wallmaterials) {
		this.wallmaterials = wallmaterials;
	}
	/**
	 * 获取：外墙材料
	 */
	public String getWallmaterials() {
		return wallmaterials;
	}
	
	/**
	 * 设置：屋顶材料
	 */
	public void setRoofmaterials(String roofmaterials) {
		this.roofmaterials = roofmaterials;
	}
	/**
	 * 获取：屋顶材料
	 */
	public String getRoofmaterials() {
		return roofmaterials;
	}
	
	/**
	 * 设置：门窗材料
	 */
	public void setWindowmaterials(String windowmaterials) {
		this.windowmaterials = windowmaterials;
	}
	/**
	 * 获取：门窗材料
	 */
	public String getWindowmaterials() {
		return windowmaterials;
	}

	/**
	 * 设置：设计图纸
	 */
	public void setDesigndrawings(String designdrawings) {
		this.designdrawings = designdrawings;
	}
	/**
	 * 获取：设计图纸
	 */
	public String getDesigndrawings() {
		return designdrawings;
	}

	/**
	 * 设置：3D模型文件
	 */
	public void setModel3dfile(String model3dfile) {
		this.model3dfile = model3dfile;
	}
	/**
	 * 获取：3D模型文件
	 */
	public String getModel3dfile() {
		return model3dfile;
	}

	/**
	 * 设置：CAD文件
	 */
	public void setCadfile(String cadfile) {
		this.cadfile = cadfile;
	}
	/**
	 * 获取：CAD文件
	 */
	public String getCadfile() {
		return cadfile;
	}

	/**
	 * 设置：效果图
	 */
	public void setRenderingimages(String renderingimages) {
		this.renderingimages = renderingimages;
	}
	/**
	 * 获取：效果图
	 */
	public String getRenderingimages() {
		return renderingimages;
	}

	/**
	 * 设置：设计说明
	 */
	public void setDesigndescription(String designdescription) {
		this.designdescription = designdescription;
	}
	/**
	 * 获取：设计说明
	 */
	public String getDesigndescription() {
		return designdescription;
	}

	/**
	 * 设置：设计师
	 */
	public void setDesigner(String designer) {
		this.designer = designer;
	}
	/**
	 * 获取：设计师
	 */
	public String getDesigner() {
		return designer;
	}

	/**
	 * 设置：设计日期
	 */
	public void setDesigndate(Date designdate) {
		this.designdate = designdate;
	}
	/**
	 * 获取：设计日期
	 */
	public Date getDesigndate() {
		return designdate;
	}

	/**
	 * 设置：审核状态
	 */
	public void setAuditstatus(String auditstatus) {
		this.auditstatus = auditstatus;
	}
	/**
	 * 获取：审核状态
	 */
	public String getAuditstatus() {
		return auditstatus;
	}

	/**
	 * 设置：审核意见
	 */
	public void setAuditcomments(String auditcomments) {
		this.auditcomments = auditcomments;
	}
	/**
	 * 获取：审核意见
	 */
	public String getAuditcomments() {
		return auditcomments;
	}

	/**
	 * 设置：预算金额
	 */
	public void setBudgetamount(Double budgetamount) {
		this.budgetamount = budgetamount;
	}
	/**
	 * 获取：预算金额
	 */
	public Double getBudgetamount() {
		return budgetamount;
	}

	/**
	 * 设置：设计版本
	 */
	public void setDesignversion(String designversion) {
		this.designversion = designversion;
	}
	/**
	 * 获取：设计版本
	 */
	public String getDesignversion() {
		return designversion;
	}

	/**
	 * 设置：是否启用
	 */
	public void setIsenabled(String isenabled) {
		this.isenabled = isenabled;
	}
	/**
	 * 获取：是否启用
	 */
	public String getIsenabled() {
		return isenabled;
	}
}
