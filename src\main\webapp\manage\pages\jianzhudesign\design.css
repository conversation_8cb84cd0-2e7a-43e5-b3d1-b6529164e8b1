/* 建筑设计管理样式 */
.design-container {
    padding: 20px;
    background: #f5f7fa;
    min-height: 100vh;
}

/* 顶部导航栏 */
.top-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: 20px 30px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.nav-left h1 {
    margin: 0;
    color: #2c3e50;
    font-size: 24px;
    font-weight: 600;
}

.nav-left h1 i {
    color: #409eff;
    margin-right: 10px;
}

.nav-right .el-button {
    margin-left: 10px;
}

/* 统计卡片 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card:nth-child(2) {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card:nth-child(3) {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(4) {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon {
    font-size: 40px;
    margin-right: 20px;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 28px;
    font-weight: 700;
}

.stat-content p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
}

/* 搜索筛选区域 */
.search-filter {
    background: #fff;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

/* 视图切换 */
.view-toggle {
    background: #fff;
    padding: 15px 20px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    text-align: center;
}

/* 表格视图 */
.table-view {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-view .el-table {
    border-radius: 12px;
}

.table-view .el-table th {
    background: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
}

/* 卡片视图 */
.card-view {
    margin-bottom: 20px;
}

.design-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.design-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card-image:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card-image:hover .image-overlay {
    opacity: 1;
}

.image-overlay .el-button {
    margin: 0 10px;
}

.card-content {
    padding: 20px;
}

.design-info p {
    margin: 8px 0;
    color: #606266;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.design-info p i {
    width: 20px;
    margin-right: 8px;
    color: #409eff;
}

.card-actions {
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
    text-align: right;
}

.card-actions .el-button {
    margin-left: 10px;
}

/* 分页 */
.pagination {
    background: #fff;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    text-align: center;
}

/* 对话框样式 */
.el-dialog {
    border-radius: 12px;
}

.el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 12px 12px 0 0;
}

.el-dialog__title {
    color: white;
    font-weight: 600;
}

.el-dialog__headerbtn .el-dialog__close {
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .design-container {
        padding: 10px;
    }
    
    .top-nav {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .stats-cards {
        grid-template-columns: 1fr;
    }
    
    .search-filter .el-row {
        flex-direction: column;
    }
    
    .search-filter .el-col {
        margin-bottom: 10px;
    }
    
    .card-view .el-col {
        margin-bottom: 20px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.design-card {
    animation: fadeInUp 0.6s ease;
}

/* 状态标签样式 */
.el-tag.el-tag--success {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    border: none;
    color: white;
}

.el-tag.el-tag--warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
    color: white;
}

.el-tag.el-tag--danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border: none;
    color: white;
}

/* 按钮样式增强 */
.el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.el-button--success {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    border: none;
}

.el-button--info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
}

.el-button--warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
}

.el-button--danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border: none;
}
