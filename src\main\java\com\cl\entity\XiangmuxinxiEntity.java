package com.cl.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.lang.reflect.InvocationTargetException;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.beanutils.BeanUtils;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.enums.FieldFill;
import com.baomidou.mybatisplus.enums.IdType;


/**
 * 项目信息
 * 数据库通用操作实体类（普通增删改查）
 * <AUTHOR> @email 
 * @date 2024-01-25 13:22:32
 */
@TableName("xiangmuxinxi")
public class XiangmuxinxiEntity<T> implements Serializable {
	private static final long serialVersionUID = 1L;


	public XiangmuxinxiEntity() {
		
	}
	
	public XiangmuxinxiEntity(T t) {
		try {
			BeanUtils.copyProperties(this, t);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	/**
	 * 主键id
	 */
	@TableId
	private Long id;
	/**
	 * 项目编号
	 */
					
	private String xiangmubianhao;
	
	/**
	 * 项目名称
	 */
					
	private String xiangmumingcheng;
	
	/**
	 * 开工日期
	 */
				
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat 		
	private Date kaigongriqi;
	
	/**
	 * 开工地址
	 */
					
	private String kaigongdizhi;
	
	/**
	 * 项目文件
	 */
					
	private String xiangmuwenjian;
	
	/**
	 * 登记时间
	 */
				
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat 		
	private Date dengjishijian;
	
	/**
	 * 经理账号
	 */
					
	private String jinglizhanghao;
	
	/**
	 * 经理姓名
	 */

	private String jinglixingming;

	/**
	 * 建筑类型
	 */
	private String jianzhutype;

	/**
	 * 建筑面积
	 */
	private Double jianzhufarea;

	/**
	 * 建筑高度
	 */
	private Double jianzhuheight;

	/**
	 * 建筑层数
	 */
	private Integer jianzhufloors;

	/**
	 * 建筑风格
	 */
	private String jianzhustyle;

	/**
	 * 设计图纸
	 */
	private String designdrawings;

	/**
	 * 3D模型文件
	 */
	private String model3dfile;

	/**
	 * 建筑材料
	 */
	private String buildingmaterials;

	/**
	 * 预算金额
	 */
	private Double budgetamount;

	/**
	 * 项目状态
	 */
	private String projectstatus;

	/**
	 * 完工日期
	 */
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat
	private Date wangongriqi;
	
	
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat
	private Date addtime;

	public Date getAddtime() {
		return addtime;
	}
	public void setAddtime(Date addtime) {
		this.addtime = addtime;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	/**
	 * 设置：项目编号
	 */
	public void setXiangmubianhao(String xiangmubianhao) {
		this.xiangmubianhao = xiangmubianhao;
	}
	/**
	 * 获取：项目编号
	 */
	public String getXiangmubianhao() {
		return xiangmubianhao;
	}
	/**
	 * 设置：项目名称
	 */
	public void setXiangmumingcheng(String xiangmumingcheng) {
		this.xiangmumingcheng = xiangmumingcheng;
	}
	/**
	 * 获取：项目名称
	 */
	public String getXiangmumingcheng() {
		return xiangmumingcheng;
	}
	/**
	 * 设置：开工日期
	 */
	public void setKaigongriqi(Date kaigongriqi) {
		this.kaigongriqi = kaigongriqi;
	}
	/**
	 * 获取：开工日期
	 */
	public Date getKaigongriqi() {
		return kaigongriqi;
	}
	/**
	 * 设置：开工地址
	 */
	public void setKaigongdizhi(String kaigongdizhi) {
		this.kaigongdizhi = kaigongdizhi;
	}
	/**
	 * 获取：开工地址
	 */
	public String getKaigongdizhi() {
		return kaigongdizhi;
	}
	/**
	 * 设置：项目文件
	 */
	public void setXiangmuwenjian(String xiangmuwenjian) {
		this.xiangmuwenjian = xiangmuwenjian;
	}
	/**
	 * 获取：项目文件
	 */
	public String getXiangmuwenjian() {
		return xiangmuwenjian;
	}
	/**
	 * 设置：登记时间
	 */
	public void setDengjishijian(Date dengjishijian) {
		this.dengjishijian = dengjishijian;
	}
	/**
	 * 获取：登记时间
	 */
	public Date getDengjishijian() {
		return dengjishijian;
	}
	/**
	 * 设置：经理账号
	 */
	public void setJinglizhanghao(String jinglizhanghao) {
		this.jinglizhanghao = jinglizhanghao;
	}
	/**
	 * 获取：经理账号
	 */
	public String getJinglizhanghao() {
		return jinglizhanghao;
	}
	/**
	 * 设置：经理姓名
	 */
	public void setJinglixingming(String jinglixingming) {
		this.jinglixingming = jinglixingming;
	}
	/**
	 * 获取：经理姓名
	 */
	public String getJinglixingming() {
		return jinglixingming;
	}

	/**
	 * 设置：建筑类型
	 */
	public void setJianzhutype(String jianzhutype) {
		this.jianzhutype = jianzhutype;
	}
	/**
	 * 获取：建筑类型
	 */
	public String getJianzhutype() {
		return jianzhutype;
	}

	/**
	 * 设置：建筑面积
	 */
	public void setJianzhufarea(Double jianzhufarea) {
		this.jianzhufarea = jianzhufarea;
	}
	/**
	 * 获取：建筑面积
	 */
	public Double getJianzhufarea() {
		return jianzhufarea;
	}

	/**
	 * 设置：建筑高度
	 */
	public void setJianzhuheight(Double jianzhuheight) {
		this.jianzhuheight = jianzhuheight;
	}
	/**
	 * 获取：建筑高度
	 */
	public Double getJianzhuheight() {
		return jianzhuheight;
	}

	/**
	 * 设置：建筑层数
	 */
	public void setJianzhufloors(Integer jianzhufloors) {
		this.jianzhufloors = jianzhufloors;
	}
	/**
	 * 获取：建筑层数
	 */
	public Integer getJianzhufloors() {
		return jianzhufloors;
	}

	/**
	 * 设置：建筑风格
	 */
	public void setJianzhustyle(String jianzhustyle) {
		this.jianzhustyle = jianzhustyle;
	}
	/**
	 * 获取：建筑风格
	 */
	public String getJianzhustyle() {
		return jianzhustyle;
	}

	/**
	 * 设置：设计图纸
	 */
	public void setDesigndrawings(String designdrawings) {
		this.designdrawings = designdrawings;
	}
	/**
	 * 获取：设计图纸
	 */
	public String getDesigndrawings() {
		return designdrawings;
	}

	/**
	 * 设置：3D模型文件
	 */
	public void setModel3dfile(String model3dfile) {
		this.model3dfile = model3dfile;
	}
	/**
	 * 获取：3D模型文件
	 */
	public String getModel3dfile() {
		return model3dfile;
	}

	/**
	 * 设置：建筑材料
	 */
	public void setBuildingmaterials(String buildingmaterials) {
		this.buildingmaterials = buildingmaterials;
	}
	/**
	 * 获取：建筑材料
	 */
	public String getBuildingmaterials() {
		return buildingmaterials;
	}

	/**
	 * 设置：预算金额
	 */
	public void setBudgetamount(Double budgetamount) {
		this.budgetamount = budgetamount;
	}
	/**
	 * 获取：预算金额
	 */
	public Double getBudgetamount() {
		return budgetamount;
	}

	/**
	 * 设置：项目状态
	 */
	public void setProjectstatus(String projectstatus) {
		this.projectstatus = projectstatus;
	}
	/**
	 * 获取：项目状态
	 */
	public String getProjectstatus() {
		return projectstatus;
	}

	/**
	 * 设置：完工日期
	 */
	public void setWangongriqi(Date wangongriqi) {
		this.wangongriqi = wangongriqi;
	}
	/**
	 * 获取：完工日期
	 */
	public Date getWangongriqi() {
		return wangongriqi;
	}

}
