/* 仪表板样式 */
.dashboard-container {
    padding: 20px;
    background: #f5f7fa;
    min-height: 100vh;
}

/* 欢迎横幅 */
.welcome-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 16px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.banner-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.welcome-text h1 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 700;
}

.welcome-text p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

.weather-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.weather-icon {
    font-size: 40px;
    opacity: 0.8;
}

.weather-details {
    display: flex;
    flex-direction: column;
}

.temperature {
    font-size: 24px;
    font-weight: 600;
}

.weather-desc {
    font-size: 14px;
    opacity: 0.8;
}

/* 核心指标网格 */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background: white;
    padding: 25px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.metric-card.projects {
    border-left: 4px solid #667eea;
}

.metric-card.designs {
    border-left: 4px solid #f093fb;
}

.metric-card.budget {
    border-left: 4px solid #43e97b;
}

.metric-card.progress {
    border-left: 4px solid #4facfe;
}

.metric-icon {
    font-size: 40px;
    margin-right: 20px;
    opacity: 0.8;
}

.metric-card.projects .metric-icon {
    color: #667eea;
}

.metric-card.designs .metric-icon {
    color: #f093fb;
}

.metric-card.budget .metric-icon {
    color: #43e97b;
}

.metric-card.progress .metric-icon {
    color: #4facfe;
}

.metric-content {
    flex: 1;
}

.metric-content h3 {
    margin: 0 0 5px 0;
    font-size: 32px;
    font-weight: 700;
    color: #2c3e50;
}

.metric-content p {
    margin: 0 0 8px 0;
    color: #7f8c8d;
    font-size: 14px;
}

.metric-change {
    font-size: 12px;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
}

.metric-change.positive {
    background: #d4edda;
    color: #155724;
}

.metric-change.negative {
    background: #f8d7da;
    color: #721c24;
}

.metric-chart {
    width: 80px;
    height: 40px;
}

.mini-chart {
    width: 100%;
    height: 100%;
}

/* 内容卡片 */
.content-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    padding: 20px 25px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.card-header h3 i {
    margin-right: 10px;
    color: #667eea;
}

.chart-container {
    padding: 20px 25px;
}

/* 快速操作 */
.quick-actions {
    padding: 20px 25px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 2px dashed #e1e8ed;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-item:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.action-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-bottom: 10px;
}

.action-item span {
    font-size: 14px;
    color: #2c3e50;
    font-weight: 500;
}

/* 待办事项 */
.todo-list {
    padding: 20px 25px;
}

.todo-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.todo-item:last-child {
    border-bottom: none;
}

.todo-item span {
    flex: 1;
    margin-left: 12px;
    color: #2c3e50;
}

.todo-item span.completed {
    text-decoration: line-through;
    color: #95a5a6;
}

.todo-priority {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 600;
}

.todo-priority.high {
    background: #ffebee;
    color: #c62828;
}

.todo-priority.medium {
    background: #fff3e0;
    color: #ef6c00;
}

.todo-priority.low {
    background: #e8f5e8;
    color: #2e7d32;
}

/* 项目状态 */
.project-status {
    padding: 20px 25px;
}

.status-item {
    margin-bottom: 20px;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-label {
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 5px;
}

.status-value {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
}

.status-bar {
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;
}

.status-progress {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.status-progress.completed {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.status-progress.paused {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* 活动列表 */
.activity-list {
    padding: 20px 25px;
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
    font-size: 16px;
}

.activity-icon.project {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.activity-icon.design {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.activity-icon.material {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.activity-icon.system {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.activity-content {
    flex: 1;
}

.activity-title {
    margin: 0 0 5px 0;
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.activity-desc {
    margin: 0 0 8px 0;
    color: #7f8c8d;
    font-size: 13px;
    line-height: 1.4;
}

.activity-time {
    font-size: 12px;
    color: #95a5a6;
}

/* 通知列表 */
.notification-list {
    padding: 20px 25px;
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: background 0.3s ease;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background: #f8f9ff;
}

.notification-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: white;
    font-size: 14px;
}

.notification-icon.info {
    background: #3498db;
}

.notification-icon.warning {
    background: #f39c12;
}

.notification-icon.success {
    background: #27ae60;
}

.notification-icon.error {
    background: #e74c3c;
}

.notification-content {
    flex: 1;
}

.notification-title {
    margin: 0 0 5px 0;
    font-size: 14px;
    color: #2c3e50;
    font-weight: 500;
}

.notification-time {
    font-size: 12px;
    color: #95a5a6;
}

.notification-badge {
    margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 10px;
    }
    
    .banner-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .main-content .el-col {
        margin-bottom: 20px;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.metric-card,
.content-card {
    animation: fadeInUp 0.6s ease;
}

/* 滚动条样式 */
.activity-list::-webkit-scrollbar,
.notification-list::-webkit-scrollbar {
    width: 6px;
}

.activity-list::-webkit-scrollbar-track,
.notification-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb,
.notification-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb:hover,
.notification-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
