/* 建筑材料库样式 */
.material-container {
    padding: 20px;
    background: #f5f7fa;
    min-height: 100vh;
}

/* 顶部导航栏 */
.top-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: 20px 30px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.nav-left h1 {
    margin: 0;
    color: #2c3e50;
    font-size: 24px;
    font-weight: 600;
}

.nav-left h1 i {
    color: #e67e22;
    margin-right: 10px;
}

.nav-right .el-button {
    margin-left: 10px;
}

/* 统计卡片 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card:nth-child(2) {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.stat-card:nth-child(3) {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
}

.stat-card:nth-child(4) {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.stat-icon {
    font-size: 40px;
    margin-right: 20px;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 28px;
    font-weight: 700;
}

.stat-content p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
}

/* 搜索筛选区域 */
.search-filter {
    background: #fff;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

/* 视图切换 */
.view-toggle {
    background: #fff;
    padding: 15px 20px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    text-align: center;
}

/* 表格视图 */
.table-view {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-view .el-table {
    border-radius: 12px;
}

.table-view .el-table th {
    background: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
}

/* 库存状态样式 */
.stock-normal {
    color: #67c23a;
    font-weight: 600;
}

.stock-warning {
    color: #e6a23c;
    font-weight: 600;
}

.stock-shortage {
    color: #f56c6c;
    font-weight: 600;
}

/* 卡片视图 */
.card-view {
    margin-bottom: 20px;
}

.material-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.material-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card-image:hover img {
    transform: scale(1.05);
}

.stock-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.stock-badge.stock-warning {
    background: rgba(230, 162, 60, 0.9);
}

.stock-badge.stock-shortage {
    background: rgba(245, 108, 108, 0.9);
}

.card-content {
    padding: 20px;
}

.material-info p {
    margin: 8px 0;
    color: #606266;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.material-info p i {
    width: 20px;
    margin-right: 8px;
    color: #e67e22;
}

.card-actions {
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
    text-align: right;
}

.card-actions .el-button {
    margin-left: 10px;
}

/* 分页 */
.pagination {
    background: #fff;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    text-align: center;
}

/* 对话框样式 */
.el-dialog {
    border-radius: 12px;
}

.el-dialog__header {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 12px 12px 0 0;
}

.el-dialog__title {
    color: white;
    font-weight: 600;
}

.el-dialog__headerbtn .el-dialog__close {
    color: white;
}

/* 质量等级标签样式 */
.el-tag.el-tag--success {
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
    border: none;
    color: white;
}

.el-tag.el-tag--warning {
    background: linear-gradient(135deg, #e6a23c 0%, #f7ba2a 100%);
    border: none;
    color: white;
}

.el-tag.el-tag--danger {
    background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
    border: none;
    color: white;
}

.el-tag.el-tag--info {
    background: linear-gradient(135deg, #909399 0%, #b1b3b8 100%);
    border: none;
    color: white;
}

/* 按钮样式增强 */
.el-button--primary {
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    border: none;
}

.el-button--success {
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
    border: none;
}

.el-button--info {
    background: linear-gradient(135deg, #909399 0%, #b1b3b8 100%);
    border: none;
}

.el-button--warning {
    background: linear-gradient(135deg, #e6a23c 0%, #f7ba2a 100%);
    border: none;
}

.el-button--danger {
    background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
    border: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .material-container {
        padding: 10px;
    }
    
    .top-nav {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .stats-cards {
        grid-template-columns: 1fr;
    }
    
    .search-filter .el-row {
        flex-direction: column;
    }
    
    .search-filter .el-col {
        margin-bottom: 10px;
    }
    
    .card-view .el-col {
        margin-bottom: 20px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.material-card {
    animation: fadeInUp 0.6s ease;
}

/* 特殊状态样式 */
.low-stock-row {
    background-color: #fef0f0 !important;
}

.out-of-stock-row {
    background-color: #fdf6ec !important;
}

/* 图片预览样式 */
.image-preview {
    max-width: 100%;
    max-height: 400px;
    border-radius: 8px;
}

/* 文件上传区域 */
.upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    background: #fafafa;
    transition: border-color 0.3s ease;
}

.upload-area:hover {
    border-color: #409eff;
}

.upload-area.is-dragover {
    border-color: #409eff;
    background: #f0f9ff;
}

/* 材料推荐样式 */
.recommendation-item {
    padding: 15px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    margin-bottom: 10px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.recommendation-item:hover {
    background: #e3f2fd;
    border-color: #409eff;
}

.recommendation-score {
    float: right;
    color: #409eff;
    font-weight: 600;
}

/* 价格趋势图表样式 */
.price-chart {
    height: 300px;
    margin: 20px 0;
}

/* 供应商信息样式 */
.supplier-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin: 10px 0;
}

.supplier-contact {
    color: #409eff;
    text-decoration: none;
}

.supplier-contact:hover {
    text-decoration: underline;
}
